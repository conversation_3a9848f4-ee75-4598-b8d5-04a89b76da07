<?php
/**
 * Test script for OnSale Batch Processing Performance
 * 
 * This script tests the performance difference between individual
 * and batch OnSale label processing
 */

// Initialize Magento
require_once 'app/Mage.php';
Mage::app('default');

echo "=== OnSale Batch Processing Performance Test ===\n\n";

// Test categories
$testCategories = array(
    8 => 'damski-chasovnici',
    9 => 'mujki-chasovnici', 
    76 => 'novo'
);

foreach ($testCategories as $categoryId => $categoryUrlKey) {
    echo "Testing Category: {$categoryUrlKey} (ID: {$categoryId})\n";
    echo str_repeat("-", 50) . "\n";
    
    // Load category
    $category = Mage::getModel('catalog/category')->load($categoryId);
    if (!$category->getId()) {
        echo "Category not found!\n\n";
        continue;
    }
    
    // Get product collection
    $productCollection = Mage::getModel('catalog/product')
        ->getCollection()
        ->addCategoryFilter($category)
        ->addAttributeToSelect('*')
        ->setPageSize(50) // Test with 50 products
        ->setCurPage(1);
    
    $productCount = $productCollection->count();
    echo "Products in category: {$productCount}\n";
    
    if ($productCount == 0) {
        echo "No products found in category!\n\n";
        continue;
    }
    
    // Test 1: Individual Processing (Original Method)
    echo "\n1. Testing Individual Processing...\n";
    $startTime = microtime(true);
    $individualLabels = array();
    
    foreach ($productCollection as $product) {
        $labelHtml = Mage::helper('onsale')->getCategoryLabelHtml($product);
        $individualLabels[$product->getId()] = $labelHtml;
    }
    
    $individualTime = microtime(true) - $startTime;
    echo "   Time: " . round($individualTime * 1000, 2) . " ms\n";
    echo "   Labels generated: " . count($individualLabels) . "\n";
    
    // Test 2: Batch Processing (New Method)
    echo "\n2. Testing Batch Processing...\n";
    $startTime = microtime(true);
    
    $batchLabels = Mage::helper('onsale')->getBatchCategoryLabelsHtml($productCollection);
    
    $batchTime = microtime(true) - $startTime;
    echo "   Time: " . round($batchTime * 1000, 2) . " ms\n";
    echo "   Labels generated: " . count($batchLabels) . "\n";
    
    // Performance comparison
    $improvement = $individualTime > 0 ? (($individualTime - $batchTime) / $individualTime) * 100 : 0;
    echo "\n3. Performance Results:\n";
    echo "   Individual: " . round($individualTime * 1000, 2) . " ms\n";
    echo "   Batch:      " . round($batchTime * 1000, 2) . " ms\n";
    echo "   Improvement: " . round($improvement, 1) . "%\n";
    
    // Verify results match
    $resultsMatch = true;
    $labelsWithContent = 0;
    foreach ($individualLabels as $productId => $individualLabel) {
        $batchLabel = isset($batchLabels[$productId]) ? $batchLabels[$productId] : '';
        if (trim($individualLabel) !== trim($batchLabel)) {
            $resultsMatch = false;
            break;
        }
        if (!empty(trim($individualLabel))) {
            $labelsWithContent++;
        }
    }
    
    echo "   Results match: " . ($resultsMatch ? "YES" : "NO") . "\n";
    echo "   Labels with content: {$labelsWithContent}\n";
    
    echo "\n" . str_repeat("=", 60) . "\n\n";
}

echo "Test completed!\n";
echo "\nConfiguration Status:\n";
echo "Batch processing enabled: " . (Mage::getStoreConfigFlag('onsale/performance/enable_batch_processing') ? "YES" : "NO") . "\n";
