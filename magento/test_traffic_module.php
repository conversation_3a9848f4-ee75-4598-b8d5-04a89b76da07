<?php
/**
 * Test script for PFG Analytics Traffic Module
 * 
 * This script tests the basic functionality of the traffic analytics module
 * including database setup, model functionality, and data collection.
 */

// Initialize Magento
require_once 'app/Mage.php';
Mage::app();

echo "=== PFG Analytics Traffic Module Test ===\n\n";

try {
    // Test 1: Check if module is enabled
    echo "1. Testing module status...\n";
    $helper = Mage::helper('pfg_analytics');
    $isEnabled = $helper->isEnabled();
    $isTrafficEnabled = $helper->isTrafficEnabled();
    
    echo "   - PFG Analytics enabled: " . ($isEnabled ? 'YES' : 'NO') . "\n";
    echo "   - Traffic Analytics enabled: " . ($isTrafficEnabled ? 'YES' : 'NO') . "\n";
    
    // Test 2: Check database table
    echo "\n2. Testing database setup...\n";
    $resource = Mage::getSingleton('core/resource');
    $readConnection = $resource->getConnection('core_read');
    $tableName = $resource->getTableName('pfg_analytics/traffic_summary');
    
    $tableExists = $readConnection->isTableExists($tableName);
    echo "   - Traffic summary table exists: " . ($tableExists ? 'YES' : 'NO') . "\n";
    echo "   - Table name: " . $tableName . "\n";
    
    if ($tableExists) {
        $describe = $readConnection->describeTable($tableName);
        echo "   - Table columns: " . implode(', ', array_keys($describe)) . "\n";
    }
    
    // Test 3: Test traffic model
    echo "\n3. Testing traffic model...\n";
    $trafficModel = Mage::getModel('pfg_analytics/traffic');
    echo "   - Traffic model loaded: " . (is_object($trafficModel) ? 'YES' : 'NO') . "\n";
    echo "   - Model class: " . get_class($trafficModel) . "\n";
    
    // Test 4: Test data collection
    echo "\n4. Testing data collection...\n";
    $fromDate = date('Y-m-d', strtotime('-30 days'));
    $toDate = date('Y-m-d');
    
    echo "   - Testing date range: {$fromDate} to {$toDate}\n";
    
    $trafficData = $trafficModel->getTrafficData($fromDate, $toDate);
    
    if (isset($trafficData['error'])) {
        echo "   - Data collection error: " . $trafficData['error'] . "\n";
    } else {
        echo "   - Total visits: " . $trafficData['total_visits'] . "\n";
        echo "   - Total unique visitors: " . $trafficData['total_unique_visitors'] . "\n";
        echo "   - Total page views: " . $trafficData['total_page_views'] . "\n";
        echo "   - Daily data points: " . count($trafficData['daily_data']) . "\n";
    }
    
    // Test 5: Test helper methods
    echo "\n5. Testing helper methods...\n";
    $dataSources = $helper->getTrafficDataSources();
    echo "   - Available data sources: " . implode(', ', array_keys($dataSources)) . "\n";
    
    $defaultSource = $helper->getDefaultTrafficSource();
    echo "   - Default data source: " . $defaultSource . "\n";
    
    $retentionDays = $helper->getTrafficDataRetentionDays();
    echo "   - Data retention days: " . $retentionDays . "\n";
    
    // Test 6: Test configuration
    echo "\n6. Testing configuration...\n";
    $trafficEnabled = Mage::getStoreConfig('pfg_analytics/traffic/enabled');
    $defaultDateRange = Mage::getStoreConfig('pfg_analytics/traffic/default_date_range');
    
    echo "   - Traffic enabled config: " . ($trafficEnabled ? 'YES' : 'NO') . "\n";
    echo "   - Default date range config: " . ($defaultDateRange ?: 'Not set') . "\n";
    
    // Test 7: Test visitor data availability
    echo "\n7. Testing visitor data availability...\n";
    $visitorTable = $resource->getTableName('log/visitor');
    $visitorInfoTable = $resource->getTableName('log/visitor_info');
    $reportEventTable = $resource->getTableName('report/event');
    
    echo "   - log_visitor table exists: " . ($readConnection->isTableExists($visitorTable) ? 'YES' : 'NO') . "\n";
    echo "   - log_visitor_info table exists: " . ($readConnection->isTableExists($visitorInfoTable) ? 'YES' : 'NO') . "\n";
    echo "   - report_event table exists: " . ($readConnection->isTableExists($reportEventTable) ? 'YES' : 'NO') . "\n";
    
    if ($readConnection->isTableExists($visitorTable)) {
        $visitorCount = $readConnection->fetchOne("SELECT COUNT(*) FROM {$visitorTable}");
        echo "   - Total visitor records: " . $visitorCount . "\n";
        
        $recentVisitors = $readConnection->fetchOne("SELECT COUNT(*) FROM {$visitorTable} WHERE first_visit_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)");
        echo "   - Recent visitors (30 days): " . $recentVisitors . "\n";
    }
    
    echo "\n=== Test completed successfully! ===\n";
    
} catch (Exception $e) {
    echo "\n=== ERROR ===\n";
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    echo "Trace:\n" . $e->getTraceAsString() . "\n";
}
