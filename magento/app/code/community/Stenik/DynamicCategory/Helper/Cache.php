<?php
/**
 * Cache helper for Dynamic Categories
 * 
 * @package Stenik_DynamicCategory
 * <AUTHOR> Stenik Group Ltd. <<EMAIL>>
 */

class Stenik_DynamicCategory_Helper_Cache extends Mage_Core_Helper_Abstract
{
    /**
     * Check if dynamic category caching is enabled
     *
     * @return bool
     */
    public function isCacheEnabled()
    {
        return Mage::app()->useCache('stenik_dynamic_category');
    }
    
    /**
     * Get cache statistics for admin display
     *
     * @return array
     */
    public function getCacheStatistics()
    {
        if (!$this->isCacheEnabled()) {
            return array(
                'enabled' => false,
                'message' => 'Dynamic Category Cache is disabled'
            );
        }
        
        try {
            $cache = Mage::app()->getCache();
            $cacheKeys = $this->_getCacheKeys();
            
            return array(
                'enabled' => true,
                'total_entries' => count($cacheKeys),
                'cache_size' => $this->_calculateCacheSize($cacheKeys),
                'last_updated' => $this->_getLastCacheUpdate()
            );
        } catch (Exception $e) {
            return array(
                'enabled' => true,
                'error' => $e->getMessage()
            );
        }
    }
    
    /**
     * Clear all dynamic category cache
     *
     * @return bool
     */
    public function clearCache()
    {
        try {
            $cache = Mage::app()->getCache();
            $result = $cache->clean(Zend_Cache::CLEANING_MODE_MATCHING_TAG, array(
                Stenik_DynamicCategory_Model_Observer::CACHE_TAG
            ));
            
            Mage::log('Dynamic category cache cleared manually', null, 'stenik_dynamic_category.log');
            return $result;
        } catch (Exception $e) {
            Mage::logException($e);
            return false;
        }
    }
    
    /**
     * Get cache hit ratio for performance monitoring
     *
     * @return float
     */
    public function getCacheHitRatio()
    {
        // This would require implementing cache hit/miss tracking
        // For now, return a placeholder
        return 0.0;
    }
    
    /**
     * Get all cache keys for dynamic categories
     *
     * @return array
     */
    protected function _getCacheKeys()
    {
        // This is a simplified implementation
        // In a real scenario, you'd need to track cache keys
        return array();
    }
    
    /**
     * Calculate total cache size
     *
     * @param array $cacheKeys
     * @return string
     */
    protected function _calculateCacheSize($cacheKeys)
    {
        $totalSize = 0;
        $cache = Mage::app()->getCache();
        
        foreach ($cacheKeys as $key) {
            $data = $cache->load($key);
            if ($data) {
                $totalSize += strlen($data);
            }
        }
        
        return $this->_formatBytes($totalSize);
    }
    
    /**
     * Get last cache update timestamp
     *
     * @return string
     */
    protected function _getLastCacheUpdate()
    {
        // This would require implementing cache timestamp tracking
        return date('Y-m-d H:i:s');
    }
    
    /**
     * Format bytes to human readable format
     *
     * @param int $bytes
     * @return string
     */
    protected function _formatBytes($bytes)
    {
        $units = array('B', 'KB', 'MB', 'GB');
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= (1 << (10 * $pow));
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
