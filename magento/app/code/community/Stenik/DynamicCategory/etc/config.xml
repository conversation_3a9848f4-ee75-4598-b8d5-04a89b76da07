<?xml version="1.0"?>
<config>
    <modules>
        <Stenik_DynamicCategory>
            <version>1.0.3</version>
        </Stenik_DynamicCategory>
    </modules>

    <global>
        <helpers>
            <stenik_dynamiccategory>
                <class>Stenik_DynamicCategory_Helper</class>
            </stenik_dynamiccategory>
        </helpers>
        <models>
            <stenik_dynamiccategory>
                <class>Stenik_DynamicCategory_Model</class>
            </stenik_dynamiccategory>
        </models>
        <events>
            <core_block_abstract_prepare_layout_before>
                <observers>
                    <stenik_dynamiccategory>
                        <type>singleton</type>
                        <class>stenik_dynamiccategory/observer</class>
                        <method>blockPrepareLayoutBefore</method>
                    </stenik_dynamiccategory>
                </observers>
            </core_block_abstract_prepare_layout_before>
        </events>
        <resources>
            <stenik_dynamiccategory_setup>
                <setup>
                    <module>Stenik_DynamicCategory</module>
                    <class>Mage_Catalog_Model_Resource_Setup</class>
                </setup>
            </stenik_dynamiccategory_setup>
        </resources>
    </global>

    <crontab>
        <jobs>
            <stenik_dynamiccategory_update_new_products>
                <schedule>
                    <cron_expr>0 * * * *</cron_expr> <!-- Every hour -->
                </schedule>
                <run>
                    <model>stenik_dynamiccategory/cron::updateNewProductsIndex</model>
                </run>
            </stenik_dynamiccategory_update_new_products>
            <stenik_dynamiccategory_cleanup_new_products>
                <schedule>
                    <cron_expr>0 2 * * *</cron_expr> <!-- Daily at 2 AM -->
                </schedule>
                <run>
                    <model>stenik_dynamiccategory/cron::cleanupNewProductsIndex</model>
                </run>
            </stenik_dynamiccategory_cleanup_new_products>
        </jobs>
    </crontab>
</config>
