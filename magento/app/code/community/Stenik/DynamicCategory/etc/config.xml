<?xml version="1.0"?>
<config>
    <modules>
        <Stenik_DynamicCategory>
            <version>1.0.3</version>
        </Stenik_DynamicCategory>
    </modules>

    <global>
        <helpers>
            <stenik_dynamiccategory>
                <class>Stenik_DynamicCategory_Helper</class>
            </stenik_dynamiccategory>
        </helpers>

        <!-- Cache type registration -->
        <cache>
            <types>
                <stenik_dynamic_category translate="label,description" module="stenik_dynamiccategory">
                    <label>Dynamic Category Cache</label>
                    <description>Cache for dynamic category filtered product results</description>
                    <tags>STENIK_DYNAMIC_CATEGORY</tags>
                </stenik_dynamic_category>
            </types>
        </cache>
        <models>
            <stenik_dynamiccategory>
                <class>Stenik_DynamicCategory_Model</class>
            </stenik_dynamiccategory>
        </models>
        <events>
            <core_block_abstract_prepare_layout_before>
                <observers>
                    <stenik_dynamiccategory>
                        <type>singleton</type>
                        <class>stenik_dynamiccategory/observer</class>
                        <method>blockPrepareLayoutBefore</method>
                    </stenik_dynamiccategory>
                </observers>
            </core_block_abstract_prepare_layout_before>

            <!-- Cache invalidation events -->
            <catalog_product_save_after>
                <observers>
                    <stenik_dynamiccategory_cache>
                        <type>singleton</type>
                        <class>stenik_dynamiccategory/observer_cache</class>
                        <method>catalogProductSaveAfter</method>
                    </stenik_dynamiccategory_cache>
                </observers>
            </catalog_product_save_after>

            <catalog_product_delete_after>
                <observers>
                    <stenik_dynamiccategory_cache>
                        <type>singleton</type>
                        <class>stenik_dynamiccategory/observer_cache</class>
                        <method>catalogProductDeleteAfter</method>
                    </stenik_dynamiccategory_cache>
                </observers>
            </catalog_product_delete_after>

            <catalog_category_change_products>
                <observers>
                    <stenik_dynamiccategory_cache>
                        <type>singleton</type>
                        <class>stenik_dynamiccategory/observer_cache</class>
                        <method>catalogCategoryProductsUpdate</method>
                    </stenik_dynamiccategory_cache>
                </observers>
            </catalog_category_change_products>
        </events>
        <resources>
            <stenik_dynamiccategory_setup>
                <setup>
                    <module>Stenik_DynamicCategory</module>
                    <class>Mage_Catalog_Model_Resource_Setup</class>
                </setup>
            </stenik_dynamiccategory_setup>
        </resources>
    </global>

    <crontab>
        <jobs>
            <stenik_dynamiccategory_update_new_products>
                <schedule>
                    <cron_expr>0 * * * *</cron_expr> <!-- Every hour -->
                </schedule>
                <run>
                    <model>stenik_dynamiccategory/cron::updateNewProductsIndex</model>
                </run>
            </stenik_dynamiccategory_update_new_products>
            <stenik_dynamiccategory_cleanup_new_products>
                <schedule>
                    <cron_expr>0 2 * * *</cron_expr> <!-- Daily at 2 AM -->
                </schedule>
                <run>
                    <model>stenik_dynamiccategory/cron::cleanupNewProductsIndex</model>
                </run>
            </stenik_dynamiccategory_cleanup_new_products>
        </jobs>
    </crontab>
</config>
