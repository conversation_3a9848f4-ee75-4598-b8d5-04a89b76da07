<?php
/**
 * Cache invalidation observer for Dynamic Categories
 * 
 * @package Stenik_DynamicCategory
 * <AUTHOR> Stenik Group Ltd. <<EMAIL>>
 */

class Stenik_DynamicCategory_Model_Observer_Cache
{
    /**
     * Invalidate dynamic category cache when product is saved
     *
     * @param Varien_Event_Observer $observer
     * @return void
     */
    public function catalogProductSaveAfter(Varien_Event_Observer $observer)
    {
        $product = $observer->getEvent()->getProduct();
        
        // Check if price-related attributes changed
        if ($this->_isPriceRelatedChange($product)) {
            $this->_invalidateDynamicCategoryCache();
        }
        
        // Check if new product status changed
        if ($this->_isNewProductStatusChange($product)) {
            $this->_invalidateDynamicCategoryCache();
            // Also update the pre-calculated new products table
            $this->_updateNewProductStatus($product);
        }
    }
    
    /**
     * Invalidate dynamic category cache when product is deleted
     *
     * @param Varien_Event_Observer $observer
     * @return void
     */
    public function catalogProductDeleteAfter(Varien_Event_Observer $observer)
    {
        $this->_invalidateDynamicCategoryCache();
    }
    
    /**
     * Invalidate dynamic category cache when category products are updated
     *
     * @param Varien_Event_Observer $observer
     * @return void
     */
    public function catalogCategoryProductsUpdate(Varien_Event_Observer $observer)
    {
        $this->_invalidateDynamicCategoryCache();
    }
    
    /**
     * Check if price-related attributes have changed
     *
     * @param Mage_Catalog_Model_Product $product
     * @return bool
     */
    protected function _isPriceRelatedChange($product)
    {
        $priceAttributes = array('price', 'special_price', 'special_from_date', 'special_to_date');
        
        foreach ($priceAttributes as $attribute) {
            if ($product->dataHasChangedFor($attribute)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Check if new product status attributes have changed
     *
     * @param Mage_Catalog_Model_Product $product
     * @return bool
     */
    protected function _isNewProductStatusChange($product)
    {
        $newProductAttributes = array('news_from_date', 'news_to_date');
        
        foreach ($newProductAttributes as $attribute) {
            if ($product->dataHasChangedFor($attribute)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Invalidate all dynamic category cache
     *
     * @return void
     */
    protected function _invalidateDynamicCategoryCache()
    {
        try {
            $cache = Mage::app()->getCache();
            $cache->clean(Zend_Cache::CLEANING_MODE_MATCHING_TAG, array(
                Stenik_DynamicCategory_Model_Observer::CACHE_TAG
            ));
            
            Mage::log('Dynamic category cache invalidated', null, 'stenik_dynamic_category.log');
        } catch (Exception $e) {
            Mage::logException($e);
        }
    }
    
    /**
     * Update new product status in pre-calculated table
     *
     * @param Mage_Catalog_Model_Product $product
     * @return void
     */
    protected function _updateNewProductStatus($product)
    {
        try {
            $connection = Mage::getSingleton('core/resource')->getConnection('core_write');
            $tableName = Mage::getSingleton('core/resource')->getTableName('stenik_dynamiccategory_new_products');
            
            // Get news date attributes
            $newsFromDate = $product->getData('news_from_date');
            $newsToDate = $product->getData('news_to_date');
            $currentDate = date('Y-m-d H:i:s');
            
            // Determine if product is new
            $isNew = 0;
            if ($newsFromDate && $newsToDate) {
                if ($currentDate >= $newsFromDate && $currentDate <= $newsToDate) {
                    $isNew = 1;
                }
            } elseif ($newsFromDate && !$newsToDate) {
                if ($currentDate >= $newsFromDate) {
                    $isNew = 1;
                }
            }
            
            // Update or insert the record
            $data = array(
                'product_id' => $product->getId(),
                'store_id' => $product->getStoreId(),
                'is_new' => $isNew,
                'news_from_date' => $newsFromDate,
                'news_to_date' => $newsToDate,
                'updated_at' => $currentDate
            );
            
            $connection->insertOnDuplicate($tableName, $data, array(
                'is_new', 'news_from_date', 'news_to_date', 'updated_at'
            ));
            
        } catch (Exception $e) {
            Mage::logException($e);
        }
    }
}
