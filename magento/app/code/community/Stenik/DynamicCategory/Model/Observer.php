<?php
/**
 * @package Stenik_DynamicCategory
 * <AUTHOR> Stenik Group Ltd. <<EMAIL>>
 */

class Stenik_DynamicCategory_Model_Observer
{
    /**
     * Block Prepare Layout Before
     *
     * @event core_block_abstract_prepare_layout_before
     *
     * @param  Varien_Event_Observer $observer
     * @return void
     */
    public function blockPrepareLayoutBefore(Varien_Event_Observer $observer)
    {
        $block = $observer->getEvent()->getBlock();

        // Performance optimization: Only process relevant blocks
        $layer = null;
        if ($block instanceof Mage_Catalog_Block_Layer_View) {
            $layer = $block->getLayer();
        } elseif ($block instanceof Mage_Catalog_Block_Product_List) {
            $layer = Mage::getSingleton('catalog/layer');
        } else {
            // Early exit for non-catalog blocks
            return;
        }

        if (!$layer) {
            return;
        }

        if ($currentCategory = $layer->getCurrentCategory()) {
            $dynamicMode = (int) $currentCategory->getData(Stenik_DynamicCategory_Helper_Data::CATEGORY_ATTRIBUTE_DYNAMICMODE);
            if (!$dynamicMode) {
                // Skip everything below if no dynamic mode is selected
                return;
            }

            $productCollection = $layer->getProductCollection();

            if (!$productCollection->getFlag('dynamic_mode_inited')) {
                // In clone mode we should update current category in layer first and reload the collection
                $dynamicMode = (int) $currentCategory->getData(Stenik_DynamicCategory_Helper_Data::CATEGORY_ATTRIBUTE_DYNAMICMODE);
                if ($dynamicMode & Stenik_DynamicCategory_Model_Entity_Attribute_Source_DynamicMode::MODE_FLAG_USE_CLONE) {
                    $dynamicClone = (int) $currentCategory->getData(Stenik_DynamicCategory_Helper_Data::CATEGORY_ATTRIBUTE_DYNAMICCLONE);
                    $iDefaultSortBy = $currentCategory->getData('default_sort_by');
                    $oClone = Mage::getModel('catalog/category')->load($dynamicClone)->setIsAnchor(true);
                    // If the category has sort by attribute set - use it for the cloned collection
                    if (!is_null($iDefaultSortBy)) {
                        $oClone->setData('default_sort_by', $iDefaultSortBy);
                    }
                    $layer->setCurrentCategory($oClone);
                    $productCollection = $layer->getProductCollection();
                }

                // Apply filters
                $this->_prepareProductCollectionUseFlags($productCollection, $currentCategory, $dynamicMode);
                $this->_prepareProductCollectionFilterFlags($productCollection, $currentCategory, $dynamicMode);
                $this->_prepareProductCollectionOrderFlags($productCollection, $currentCategory, $dynamicMode);

                $productCollection->setFlag('dynamic_mode_inited', true);
            }
        }
    }

    /**
     * Prepare Product Collection Use Flags
     *
     * @param  Mage_Eav_Model_Entity_Collection_Abstract $productCollection
     * @param  Mage_Catalog_Model_Category $currentCategory
     * @param  integer $dynamicMode
     * @return self
     */
    protected function _prepareProductCollectionUseFlags($productCollection, $currentCategory, $dynamicMode)
    {
        if ($dynamicMode & Stenik_DynamicCategory_Model_Entity_Attribute_Source_DynamicMode::MODE_FLAG_USE_PARENT) {
            # Parent's Category
            $productCollection->addCategoryFilter($currentCategory->getParentCategory());
        } elseif ($dynamicMode & Stenik_DynamicCategory_Model_Entity_Attribute_Source_DynamicMode::MODE_FLAG_USE_ROOT) {
            # All Products
            $productCollection->addCategoryFilter(
                Mage::getModel('catalog/category')->load(Mage::app()->getStore()->getRootCategoryId())->setIsAnchor(true)
            );
        } elseif ($dynamicMode & Stenik_DynamicCategory_Model_Entity_Attribute_Source_DynamicMode::MODE_FLAG_USE_CLONE) {
            # Specific category
            $dynamicClone = (int) $currentCategory->getData(Stenik_DynamicCategory_Helper_Data::CATEGORY_ATTRIBUTE_DYNAMICCLONE);
            $iDefaultSortBy = $currentCategory->getData('default_sort_by');

            $oClone = Mage::getModel('catalog/category')->load($dynamicClone)->setIsAnchor(true);
            $productCollection->addCategoryFilter($oClone);
        }

        return $this;
    }

    /**
     * Prepare Product Collection Filter Flags
     *
     * @param  Mage_Eav_Model_Entity_Collection_Abstract $productCollection
     * @param  Mage_Catalog_Model_Category $currentCategory
     * @param  integer $dynamicMode
     * @return self
     */
    protected function _prepareProductCollectionFilterFlags($productCollection, $currentCategory, $dynamicMode)
    {
        if ($dynamicMode & Stenik_DynamicCategory_Model_Entity_Attribute_Source_DynamicMode::MODE_FLAG_FILTER_DISCOUNTED_PRICE) {
            # Optimized Discounted Price Filter using JOIN instead of subquery
            $connection = $productCollection->getConnection();
            $priceIndexTable = Mage::getSingleton('core/resource')->getTableName('catalog/product_index_price');
            $storeId = Mage::app()->getStore()->getId();
            $websiteId = Mage::app()->getStore()->getWebsiteId();

            // Add price data first
            $productCollection->addPriceData();

            // Use direct WHERE conditions on the existing price index JOIN for better performance
            $productCollection->getSelect()->where(sprintf(
                '%s.final_price < %s.price',
                Mage_Catalog_Model_Resource_Product_Collection::INDEX_TABLE_ALIAS,
                Mage_Catalog_Model_Resource_Product_Collection::INDEX_TABLE_ALIAS
            ));

            // Add price range filtering for better performance
            $productCollection->getSelect()->where(sprintf(
                '%s.final_price >= ? AND %s.price >= ? AND %s.price <= ? AND (%s.price - %s.final_price) >= ?',
                Mage_Catalog_Model_Resource_Product_Collection::INDEX_TABLE_ALIAS,
                Mage_Catalog_Model_Resource_Product_Collection::INDEX_TABLE_ALIAS,
                Mage_Catalog_Model_Resource_Product_Collection::INDEX_TABLE_ALIAS,
                Mage_Catalog_Model_Resource_Product_Collection::INDEX_TABLE_ALIAS,
                Mage_Catalog_Model_Resource_Product_Collection::INDEX_TABLE_ALIAS
            ), 1.00, 1.00, 10000.00, 0.01);
        }

        if ($dynamicMode & Stenik_DynamicCategory_Model_Entity_Attribute_Source_DynamicMode::MODE_FLAG_FILTER_NEW) {
            # Ultra-Optimized New Product Filter using Pre-calculated Table
            $connection = $productCollection->getConnection();
            $storeId = Mage::app()->getStore()->getId();
            $newProductsTable = 'stenik_dynamiccategory_new_products';

            // Use the pre-calculated new products table for maximum performance
            $productCollection->getSelect()->joinInner(
                array('new_products' => $newProductsTable),
                'e.entity_id = new_products.product_id AND new_products.store_id = ' . (int)$storeId,
                array()
            )->where('new_products.is_new = 1');

            // Optional: Add news date information for display purposes
            $productCollection->getSelect()->columns(array(
                'news_from_date' => 'new_products.news_from_date',
                'news_to_date' => 'new_products.news_to_date'
            ));
        }

        return $this;
    }

    /**
     * Prepare Product Collection Order Flags
     *
     * @param  Mage_Eav_Model_Entity_Collection_Abstract $productCollection
     * @param  Mage_Catalog_Model_Category $currentCategory
     * @param  integer $dynamicMode
     * @return self
     */
    protected function _prepareProductCollectionOrderFlags($productCollection, $currentCategory, $dynamicMode)
    {
        if ($dynamicMode & Stenik_DynamicCategory_Model_Entity_Attribute_Source_DynamicMode::MODE_FLAG_ORDER_MOST_VIEWED) {
            # Optimized Most Viewed - Direct JOIN instead of temporary table
            $connection = $productCollection->getConnection();
            $viewdProductIndexTableName = Mage::getSingleton('core/resource')->getTableName('reports/viewed_product_index');

            // Use direct subquery instead of temporary table for better performance
            $productCollection->getSelect()->joinLeft(
                array('rvpi_agg' => new Zend_Db_Expr("(
                    SELECT product_id, COUNT(product_id) as views_num
                    FROM {$viewdProductIndexTableName}
                    WHERE added_at >= DATE_SUB(NOW(), INTERVAL 90 DAY)
                    GROUP BY product_id
                )")),
                'e.entity_id = rvpi_agg.product_id',
                array(
                    'views_num' => 'COALESCE(rvpi_agg.views_num, 0)',
                )
            );
            $productCollection->getSelect()->order('views_num DESC');
        }

        return $this;
    }
}
