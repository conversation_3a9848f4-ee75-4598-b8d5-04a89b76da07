<?php
/**
 * Cron Model for Stenik Dynamic Category
 * 
 * Handles periodic updates of pre-calculated data for performance optimization
 *
 * @category Stenik
 * @package  Stenik_DynamicCategory
 */
class Stenik_DynamicCategory_Model_Cron
{
    /**
     * Update new products index table
     * 
     * This method is called by cron to keep the new products table up-to-date
     * Runs every hour to ensure accurate new product detection
     */
    public function updateNewProductsIndex()
    {
        try {
            $write = Mage::getSingleton('core/resource')->getConnection('core_write');
            $read = Mage::getSingleton('core/resource')->getConnection('core_read');
            
            // Get attribute IDs for news dates
            $newsFromDateAttrId = Mage::getResourceModel('eav/entity_attribute')
                ->getIdByCode('catalog_product', 'news_from_date');
            $newsToDateAttrId = Mage::getResourceModel('eav/entity_attribute')
                ->getIdByCode('catalog_product', 'news_to_date');
            
            if (!$newsFromDateAttrId || !$newsToDateAttrId) {
                Mage::log('Stenik DynamicCategory: News date attributes not found', Zend_Log::WARN);
                return;
            }
            
            // Update the new products table with current data
            $sql = "
                INSERT INTO stenik_dynamiccategory_new_products (product_id, store_id, is_new, news_from_date, news_to_date)
                SELECT 
                    e.entity_id as product_id,
                    1 as store_id,
                    CASE 
                        WHEN (from_date.value IS NOT NULL AND from_date.value <= NOW()) 
                             AND (to_date.value IS NULL OR to_date.value >= NOW())
                        THEN 1 
                        ELSE 0 
                    END as is_new,
                    from_date.value as news_from_date,
                    to_date.value as news_to_date
                FROM catalog_product_entity e
                LEFT JOIN catalog_product_entity_datetime from_date 
                    ON e.entity_id = from_date.entity_id 
                    AND from_date.attribute_id = ?
                LEFT JOIN catalog_product_entity_datetime to_date 
                    ON e.entity_id = to_date.entity_id 
                    AND to_date.attribute_id = ?
                ON DUPLICATE KEY UPDATE
                    is_new = VALUES(is_new),
                    news_from_date = VALUES(news_from_date),
                    news_to_date = VALUES(news_to_date)
            ";
            
            $write->query($sql, array($newsFromDateAttrId, $newsToDateAttrId));
            
            // Log success
            $affectedRows = $write->rowCount();
            Mage::log("Stenik DynamicCategory: Updated new products index, {$affectedRows} products processed", Zend_Log::INFO);
            
        } catch (Exception $e) {
            Mage::log('Stenik DynamicCategory cron error: ' . $e->getMessage(), Zend_Log::ERR);
            Mage::logException($e);
        }
    }
    
    /**
     * Clean up old entries from new products table
     * 
     * Removes products that no longer exist
     */
    public function cleanupNewProductsIndex()
    {
        try {
            $write = Mage::getSingleton('core/resource')->getConnection('core_write');
            
            // Remove entries for products that no longer exist
            $sql = "
                DELETE np FROM stenik_dynamiccategory_new_products np
                LEFT JOIN catalog_product_entity e ON np.product_id = e.entity_id
                WHERE e.entity_id IS NULL
            ";
            
            $write->query($sql);
            
            $deletedRows = $write->rowCount();
            if ($deletedRows > 0) {
                Mage::log("Stenik DynamicCategory: Cleaned up {$deletedRows} orphaned new product entries", Zend_Log::INFO);
            }
            
        } catch (Exception $e) {
            Mage::log('Stenik DynamicCategory cleanup error: ' . $e->getMessage(), Zend_Log::ERR);
            Mage::logException($e);
        }
    }
}
