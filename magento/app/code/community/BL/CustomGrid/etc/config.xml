<?xml version="1.0"?>
<!--
/**
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 *
 * @category   BL
 * @package    BL_CustomGrid
 * @copyright  Copyright (c) 2012 <PERSON><PERSON><PERSON><PERSON> Leulliette <<EMAIL>>
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<config>
    <modules>
        <BL_CustomGrid>
            <version>0.9.2.1</version>
        </BL_CustomGrid>
    </modules>
    <global>
        <blocks>
            <customgrid>
                <class>BL_CustomGrid_Block</class>
            </customgrid>
        </blocks>
        <helpers>
            <customgrid>
                <class>BL_CustomGrid_Helper</class>
            </customgrid>
        </helpers>
        <models>
            <customgrid>
                <class>BL_CustomGrid_Model</class>
                <resourceModel>customgrid_mysql4</resourceModel>
            </customgrid>
            <customgrid_mysql4>
                <class>BL_CustomGrid_Model_Mysql4</class>
                <entities>
                    <grid><table>customgrid_grid</table></grid>
                    <grid_column><table>customgrid_grid_column</table></grid_column>
                    <grid_profile><table>customgrid_grid_profile</table></grid_profile>
                    <grid_role><table>customgrid_grid_role</table></grid_role>
                    <grid_user><table>customgrid_grid_user</table></grid_user>
                    <options_source><table>customgrid_options_source</table></options_source>
                    <options_source_model><table>customgrid_options_source_model</table></options_source_model>
                    <options_source_option><table>customgrid_options_source_option</table></options_source_option>
                </entities>
            </customgrid_mysql4>
        </models>
        <resources>
            <customgrid_setup>
                <setup>
                    <module>BL_CustomGrid</module>
                </setup>
                <connection>
                    <use>core_setup</use>
                </connection>
            </customgrid_setup>
        </resources>
    </global>
    <admin>
        <routers>
            <adminhtml>
                <args>
                    <modules>
                        <customgrid after="Mage_Adminhtml">BL_CustomGrid</customgrid>
                    </modules>
                </args>
            </adminhtml>
        </routers>
    </admin>
    <adminhtml>
        <events>
            <controller_action_predispatch>
                <observers>
                    <bl_customgrid>
                        <type>singleton</type>
                        <class>customgrid/observer</class>
                        <method>onControllerActionPreDispatch</method>
                    </bl_customgrid>
                </observers>
            </controller_action_predispatch>
            <controller_action_layout_load_before>
                <observers>
                    <bl_customgrid>
                        <type>singleton</type>
                        <class>customgrid/observer</class>
                        <method>beforeControllerActionLayoutLoad</method>
                    </bl_customgrid>
                </observers>
            </controller_action_layout_load_before>
            <core_block_abstract_prepare_layout_before>
                <observers>
                    <bl_customgrid>
                        <type>singleton</type>
                        <class>customgrid/observer</class>
                        <method>beforeBlockPrepareLayout</method>
                    </bl_customgrid>
                </observers>
            </core_block_abstract_prepare_layout_before>
            <core_block_abstract_to_html_before>
                <observers>
                    <bl_customgrid>
                        <type>singleton</type>
                        <class>customgrid/observer</class>
                        <method>beforeBlockToHtml</method>
                    </bl_customgrid>
                </observers>
            </core_block_abstract_to_html_before>
        </events>
        <layout>
            <updates>
                <customgrid>
                    <file>bl/customgrid.xml</file>
                </customgrid>
            </updates>
        </layout>
        <translate>
            <modules>
                <customgrid>
                    <files>
                        <default>BL_CustomGrid.csv</default>
                    </files>
                </customgrid>
            </modules>
        </translate>
    </adminhtml>
    <default>
        <customgrid>
            <global>
                <exclusions_list><![CDATA[a:4:{s:1:"1";a:2:{s:10:"block_type";s:18:"adminhtml/report_*";s:20:"rewriting_class_name";s:1:"*";}s:1:"2";a:2:{s:10:"block_type";s:36:"adminhtml/newsletter_subscriber_grid";s:20:"rewriting_class_name";s:1:"*";}s:1:"3";a:2:{s:10:"block_type";s:34:"adminhtml/newsletter_template_grid";s:20:"rewriting_class_name";s:1:"*";}s:1:"4";a:2:{s:10:"block_type";s:36:"adminhtml/system_email_template_grid";s:20:"rewriting_class_name";s:1:"*";}}]]></exclusions_list>
                <exceptions_list></exceptions_list>
                <exceptions_handling_mode>exclude</exceptions_handling_mode>
                <store_parameter>store</store_parameter>
                <sort_with_dnd>1</sort_with_dnd>
            </global>
            <customization_params>
                <pin_header>1</pin_header>
            </customization_params>
            <custom_default_params>
                <page>default</page>
                <limit>default</limit>
                <sort>default</sort>
                <dir>default</dir>
                <filter>default</filter>
            </custom_default_params>
            <custom_columns>
                <group_in_default_header>1</group_in_default_header>
            </custom_columns>
            <editor_sitemap>
                <delete_file>1</delete_file>
            </editor_sitemap>
        </customgrid>
        <customgrid_rewriters>
            <eval>
                <model>customgrid/grid_rewriter_eval</model>
                <enabled>1</enabled>
                <priority>10</priority>
                <display_errors>1</display_errors>
                <display_errors_if_success>1</display_errors_if_success>
                <log_errors>0</log_errors>
                <log_errors_if_success>0</log_errors_if_success>
            </eval>
            <file>
                <model>customgrid/grid_rewriter_file</model>
                <enabled>0</enabled>
                <priority>20</priority>
                <display_errors>1</display_errors>
                <display_errors_if_success>1</display_errors_if_success>
                <log_errors>0</log_errors>
                <log_errors_if_success>0</log_errors_if_success>
            </file>
        </customgrid_rewriters>
    </default>
</config>