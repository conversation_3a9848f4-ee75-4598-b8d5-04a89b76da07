<?php
/**
 * aheadWorks Co.
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the EULA
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://ecommerce.aheadworks.com/AW-LICENSE.txt
 *
 * =================================================================
 *                 MAGENTO EDITION USAGE NOTICE
 * =================================================================
 * This software is designed to work with Magento community edition and
 * its use on an edition other than specified is prohibited. aheadWorks does not
 * provide extension support in case of incorrect edition use.
 * =================================================================
 *
 * @category   AW
 * @package    AW_Onsale
 * @version    2.5.5
 * @copyright  Copyright (c) 2010-2012 aheadWorks Co. (http://www.aheadworks.com)
 * @license    http://ecommerce.aheadworks.com/AW-LICENSE.txt
 */

/**
 * Batch OnSale Label Processing Block
 * 
 * This block provides optimized batch processing for OnSale labels
 * to improve category page performance by reducing database queries
 * from N+1 to just a few queries total.
 */
class AW_Onsale_Block_Catalog_Product_List_Batch extends Mage_Core_Block_Template
{
    /**
     * Cached batch label HTML array
     * @var array
     */
    protected $_batchLabelsHtml = null;
    
    /**
     * Product collection for batch processing
     * @var Mage_Catalog_Model_Resource_Product_Collection
     */
    protected $_productCollection = null;
    
    /**
     * Set the product collection for batch processing
     *
     * @param Mage_Catalog_Model_Resource_Product_Collection $collection
     * @return AW_Onsale_Block_Catalog_Product_List_Batch
     */
    public function setProductCollection($collection)
    {
        $this->_productCollection = $collection;
        $this->_batchLabelsHtml = null; // Reset cache when collection changes
        return $this;
    }
    
    /**
     * Get the product collection
     *
     * @return Mage_Catalog_Model_Resource_Product_Collection
     */
    public function getProductCollection()
    {
        return $this->_productCollection;
    }
    
    /**
     * Get batch processed OnSale labels for all products
     * This method processes all products at once for optimal performance
     *
     * @return array Array of product_id => label_html
     */
    public function getBatchLabelsHtml()
    {
        if ($this->_batchLabelsHtml === null) {
            if ($this->_productCollection) {
                $this->_batchLabelsHtml = Mage::helper('onsale')->getBatchCategoryLabelsHtml($this->_productCollection);
            } else {
                $this->_batchLabelsHtml = array();
            }
        }
        
        return $this->_batchLabelsHtml;
    }
    
    /**
     * Get OnSale label HTML for a specific product
     * Uses batch processing for optimal performance
     *
     * @param Mage_Catalog_Model_Product $product
     * @return string
     */
    public function getProductLabelHtml($product)
    {
        $batchLabels = $this->getBatchLabelsHtml();
        $productId = $product->getId();
        
        return isset($batchLabels[$productId]) ? $batchLabels[$productId] : '';
    }
    
    /**
     * Check if batch processing is enabled
     * Can be used to toggle between batch and individual processing
     *
     * @return bool
     */
    public function isBatchProcessingEnabled()
    {
        return Mage::getStoreConfigFlag('onsale/performance/enable_batch_processing');
    }
    
    /**
     * Get performance statistics for debugging
     * Shows the number of products processed and queries saved
     *
     * @return array
     */
    public function getPerformanceStats()
    {
        $batchLabels = $this->getBatchLabelsHtml();
        $productCount = $this->_productCollection ? $this->_productCollection->count() : 0;
        $queriesSaved = max(0, $productCount - 2); // Batch uses ~2 queries vs N queries
        
        return array(
            'products_processed' => $productCount,
            'labels_generated' => count($batchLabels),
            'queries_saved' => $queriesSaved,
            'performance_improvement' => $productCount > 0 ? round(($queriesSaved / $productCount) * 100, 1) . '%' : '0%'
        );
    }
}
