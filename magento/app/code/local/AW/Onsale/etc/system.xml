<?xml version="1.0"?>
<config>
    <sections>
        <onsale translate="label" module="onsale">
            <label>On Sale</label>
            <tab>awall</tab>
            <frontend_type>text</frontend_type>
            <sort_order>100</sort_order>
            <show_in_default>1</show_in_default>
            <show_in_website>1</show_in_website>
            <show_in_store>1</show_in_store>
            <groups>
                <product_onsale_label translate="label">
                    <label>Product On Sale Label</label>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                    <sort_order>10</sort_order>
                    <fields>
                        <display translate="label">
                            <label>Display</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>10</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </display>
                        <position translate="label">
                            <label>Position</label>
                            <frontend_type>select</frontend_type>
                            <frontend_model>onsale/system_config_form_element_position</frontend_model>
                            <source_model>onsale/system_config_source_position</source_model>
                            <sort_order>20</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </position>
                        <image translate="label">
                            <label>Image</label>
                            <frontend_type>image</frontend_type>
                            <backend_model>adminhtml/system_config_backend_image</backend_model>
                            <upload_dir config="system/filesystem/media" scope_info="1">onsale</upload_dir>
                            <base_url type="media" scope_info="1">onsale</base_url>
                            <sort_order>30</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </image>
                        <image_path translate="label comment">
                            <label>Image Path</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>35</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment>/img/image.png or http://domain.com/img/image.png</comment>
                        </image_path>
                        <text translate="label comment">
                            <label>Text</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>40</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment>You can use predefined values in this field. Please refer to extension manual.</comment>
                        </text>
                        <threshold translate="label comment">
                            <label>Threshold (%)</label>
                            <frontend_type>text</frontend_type>
                            <backend_model>onsale/system_config_backend_percent</backend_model>
                            <sort_order>50</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment>Discount threshold to make labels appear (e.g. 10)</comment>
                        </threshold>
                    </fields>
                </product_onsale_label>
                <category_onsale_label translate="label">
                    <label>Category On Sale Label</label>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                    <sort_order>20</sort_order>
                    <fields>
                        <display translate="label">
                            <label>Display</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>1</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </display>
                        <position>
                            <label>Position</label>
                            <frontend_type>select</frontend_type>
                            <frontend_model>onsale/system_config_form_element_position</frontend_model>
                            <source_model>onsale/system_config_source_position</source_model>
                            <sort_order>20</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </position>
                        <image translate="label">
                            <label>Image</label>
                            <frontend_type>image</frontend_type>
                            <backend_model>adminhtml/system_config_backend_image</backend_model>
                            <upload_dir config="system/filesystem/media" scope_info="1">onsale</upload_dir>
                            <base_url type="media" scope_info="1">onsale</base_url>
                            <sort_order>30</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </image>
                        <image_path translate="label comment">
                            <label>Image Path</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>35</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment>/img/image.png or http://domain.com/img/image.png</comment>
                        </image_path>
                        <text translate="label comment">
                            <label>Text</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>40</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment>You can use predefined values in this field. Please refer to extension manual.</comment>
                        </text>
                        <threshold translate="label comment">
                            <label>Threshold (%)</label>
                            <frontend_type>text</frontend_type>
                            <backend_model>onsale/system_config_backend_percent</backend_model>
                            <sort_order>50</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment>Discount threshold to make labels appear (e.g. 10)</comment>
                        </threshold>
                    </fields>
                </category_onsale_label>
                <product_new_label translate="label">
                    <label>Product New Label</label>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                    <sort_order>30</sort_order>
                    <fields>
                        <display translate="label">
                            <label>Display</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>10</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </display>
                        <position translate="label">
                            <label>Position</label>
                            <frontend_type>select</frontend_type>
                            <frontend_model>onsale/system_config_form_element_position</frontend_model>
                            <source_model>onsale/system_config_source_position</source_model>
                            <sort_order>20</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </position>
                        <image translate="label">
                            <label>Image</label>
                            <frontend_type>image</frontend_type>
                            <backend_model>adminhtml/system_config_backend_image</backend_model>
                            <upload_dir config="system/filesystem/media" scope_info="1">onsale</upload_dir>
                            <base_url type="media" scope_info="1">onsale</base_url>
                            <sort_order>30</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </image>
                        <image_path translate="label comment">
                            <label>Image Path</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>35</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment>/img/image.png or http://domain.com/img/image.png</comment>
                        </image_path>
                        <text translate="label comment">
                            <label>Text</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>40</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment>You can use predefined values in this field. Please refer to extension manual.</comment>
                        </text>
                        <days translate="label">
                            <label>Days</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>50</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </days>
                        <overrides translate="label">
                            <label>Overrides On Sale Label</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>60</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </overrides>
                        <overrides_native_new translate="label">
                            <label>Overrides Native Product "New" Period</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>70</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </overrides_native_new>
                    </fields>
                </product_new_label>
                <category_new_label translate="label">
                    <label>Category New Label</label>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                    <sort_order>40</sort_order>
                    <fields>
                        <display translate="label">
                            <label>Display</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>10</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </display>
                        <position translate="label">
                            <label>Position</label>
                            <frontend_type>select</frontend_type>
                            <frontend_model>onsale/system_config_form_element_position</frontend_model>
                            <source_model>onsale/system_config_source_position</source_model>
                            <sort_order>20</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </position>
                        <image translate="label">
                            <label>Image</label>
                            <frontend_type>image</frontend_type>
                            <backend_model>adminhtml/system_config_backend_image</backend_model>
                            <upload_dir config="system/filesystem/media" scope_info="1">onsale</upload_dir>
                            <base_url type="media" scope_info="1">onsale</base_url>
                            <sort_order>30</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </image>
                        <image_path translate="label comment">
                            <label>Image Path</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>35</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment>/img/image.png or http://domain.com/img/image.png</comment>
                        </image_path>
                        <text translate="label comment">
                            <label>Text</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>40</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment>You can use predefined values in this field. Please refer to extension manual.</comment>
                        </text>
                        <days translate="label">
                            <label>Days</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>50</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </days>
                        <overrides translate="label">
                            <label>Overrides On Sale Label</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>60</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </overrides>
                        <overrides_native_new translate="label">
                            <label>Overrides Native Product "New" Period</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>70</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </overrides_native_new>
                    </fields>
                </category_new_label>
                <performance translate="label">
                    <label>Performance Settings</label>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                    <sort_order>100</sort_order>
                    <fields>
                        <enable_batch_processing translate="label comment">
                            <label>Enable Batch Label Processing</label>
                            <comment>Improves category page performance by processing all OnSale labels at once instead of individually. Recommended for categories with many products.</comment>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>10</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </enable_batch_processing>
                        <enable_label_caching translate="label comment">
                            <label>Enable Label Caching</label>
                            <comment>Caches OnSale label HTML to improve performance on subsequent page loads. Cache is automatically invalidated when rules or products change.</comment>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>20</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <depends>
                                <enable_batch_processing>1</enable_batch_processing>
                            </depends>
                        </enable_label_caching>
                        <cache_lifetime translate="label comment">
                            <label>Cache Lifetime (seconds)</label>
                            <comment>How long to cache OnSale label HTML. Default: 3600 seconds (1 hour).</comment>
                            <frontend_type>text</frontend_type>
                            <validate>validate-number validate-greater-than-zero</validate>
                            <sort_order>30</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <depends>
                                <enable_label_caching>1</enable_label_caching>
                            </depends>
                        </cache_lifetime>
                    </fields>
                </performance>
            </groups>
        </onsale>
    </sections>
</config>