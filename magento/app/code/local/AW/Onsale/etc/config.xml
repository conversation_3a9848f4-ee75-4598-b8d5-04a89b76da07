<?xml version="1.0"?>
<config>
    <modules>
        <AW_Onsale>
            <version>2.5.5</version><platform>ce</platform>
        </AW_Onsale>
    </modules>
    <global>
        <blocks>
            <onsale>
                <class>AW_Onsale_Block</class>
            </onsale>
        </blocks>
        <helpers>
            <onsale>
                <class>AW_Onsale_Helper</class>
            </onsale>
        </helpers>
        <resources>
            <onsale_setup>
                <setup>
                    <module>AW_Onsale</module>
                </setup>
                <connection>
                    <use>core_setup</use>
                </connection>
            </onsale_setup>
            <onsale_write>
                <connection>
                    <use>core_write</use>
                </connection>
            </onsale_write>
            <onsale_read>
                <connection>
                    <use>core_read</use>
                </connection>
            </onsale_read>
        </resources>
        <events>
            <catalog_product_save_after>
                <observers>
                    <onsale_cache_invalidate_product>
                        <class>onsale/observer_cache</class>
                        <method>catalogProductSaveAfter</method>
                    </onsale_cache_invalidate_product>
                </observers>
            </catalog_product_save_after>
            <application_clean_cache>
                <observers>
                    <onsale_cache_clean>
                        <class>onsale/observer_cache</class>
                        <method>applicationCleanCache</method>
                    </onsale_cache_clean>
                </observers>
            </application_clean_cache>
        </events>
        <models>
            <onsale>
                <class>AW_Onsale_Model</class>
                <resourceModel>onsale_resource</resourceModel>
            </onsale>
            <onsale_resource>
                <class>AW_Onsale_Model_Resource</class>
                <entities>
                    <rule>
                        <table>aw_onsale_rule</table>
                    </rule>
                    <rule_product>
                        <table>aw_onsale_product</table>
                    </rule_product>
                    <website>
                        <table>aw_onsale_website</table>
                    </website>
                    <customer_group>
                        <table>aw_onsale_customer_group</table>
                    </customer_group>
                </entities>
            </onsale_resource>
        </models>
        <events>
            <sales_order_save_after>
                <observers>
                    <onsale_sales_order_save_after>
                        <class>onsale/observer</class>
                        <method>salesOrderSaveAfter</method>
                    </onsale_sales_order_save_after>
                </observers>
            </sales_order_save_after>
        </events>
    </global>
    <frontend>
        <layout>
            <updates>
                <onsale>
                    <file>onsale.xml</file>
                </onsale>
            </updates>
        </layout>
    </frontend>
    <admin>
        <routers>
            <adminhtml>
                <args>
                    <modules>
                        <awonsale after="Mage_Adminhtml">AW_Onsale_Adminhtml</awonsale>
                    </modules>
                </args>
            </adminhtml>
        </routers>
    </admin>
    <adminhtml>
        <layout>
            <updates>
                <onsale>
                    <file>aw_onsale.xml</file>
                </onsale>
            </updates>
        </layout>
        <acl>
            <resources>
                <all>
                    <title>Allow Everything</title>
                </all>
                <admin>
                    <children>
                        <system>
                            <children>
                                <config>
                                    <children>
                                        <onsale>
                                            <title>aheadWorks - On Sale</title>
                                        </onsale>
                                    </children>
                                </config>
                            </children>
                        </system>
                    </children>
                </admin>
            </resources>
        </acl>
        <translate>
            <modules>
                <AW_Onsale>
                    <files>
                        <default>AW_Onsale.csv</default>
                    </files>
                </AW_Onsale>
            </modules>
        </translate>
        <events>
            <controller_action_predispatch>
                <observers>
                    <onsale_predispatch>
                        <class>onsale/observer</class>
                        <method>controllerPredispatch</method>
                    </onsale_predispatch>
                </observers>
            </controller_action_predispatch>
            <cataloginventory_stock_item_save_after>
                <observers>
                    <onsale_cataloginventory_stock_item_save_after>
                        <class>onsale/observer</class>
                        <method>catalogInventoryStockItemSaveAfter</method>
                    </onsale_cataloginventory_stock_item_save_after>
                </observers>
            </cataloginventory_stock_item_save_after>
        </events>
    </adminhtml>
    <default>
        <onsale>
            <product_onsale_label>
                <display>1</display>
                <position>BR</position>
                <text>SALE</text>
                <threshold>0</threshold>
            </product_onsale_label>
            <category_onsale_label>
                <display>1</display>
                <position>BR</position>
                <text>SALE</text>
                <threshold>0</threshold>
            </category_onsale_label>
            <product_new_label>
                <display>1</display>
                <position>BR</position>
                <text>NEW</text>
                <days>30</days>
                <overrides>1</overrides>
                <overrides_native_new>0</overrides_native_new>
            </product_new_label>
            <category_new_label>
                <display>1</display>
                <position>BR</position>
                <text>NEW</text>
                <days>30</days>
                <overrides>1</overrides>
                <overrides_native_new>0</overrides_native_new>
            </category_new_label>
            <performance>
                <enable_batch_processing>1</enable_batch_processing>
                <enable_label_caching>1</enable_label_caching>
                <cache_lifetime>3600</cache_lifetime>
            </performance>
        </onsale>
    </default>
</config>
