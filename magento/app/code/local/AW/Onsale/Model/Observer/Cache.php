<?php
/**
 * aheadWorks Co.
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the EULA
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://ecommerce.aheadworks.com/AW-LICENSE.txt
 *
 * =================================================================
 *                 MAGENTO EDITION USAGE NOTICE
 * =================================================================
 * This software is designed to work with Magento community edition and
 * its use on an edition other than specified is prohibited. aheadWorks does not
 * provide extension support in case of incorrect edition use.
 * =================================================================
 *
 * @category   AW
 * @package    AW_Onsale
 * @version    2.5.5
 * @copyright  Copyright (c) 2010-2012 aheadWorks Co. (http://www.aheadworks.com)
 * @license    http://ecommerce.aheadworks.com/AW-LICENSE.txt
 */

/**
 * OnSale Cache Observer
 * 
 * Handles cache invalidation for OnSale labels when rules or products change
 */
class AW_Onsale_Model_Observer_Cache
{
    /**
     * Cache tag for OnSale labels
     */
    const CACHE_TAG = 'ONSALE_LABELS';
    
    /**
     * Invalidate OnSale label cache when a rule is saved
     *
     * @param Varien_Event_Observer $observer
     * @return AW_Onsale_Model_Observer_Cache
     */
    public function onSaleRuleSaveAfter($observer)
    {
        try {
            $rule = $observer->getEvent()->getRule();
            if ($rule && $rule->getId()) {
                // Clear all OnSale label cache when rules change
                Mage::app()->getCache()->clean(
                    Zend_Cache::CLEANING_MODE_MATCHING_TAG,
                    array(self::CACHE_TAG)
                );
                
                Mage::log('OnSale label cache cleared after rule save: ' . $rule->getId(), null, 'onsale_cache.log');
            }
        } catch (Exception $e) {
            Mage::logException($e);
        }
        
        return $this;
    }
    
    /**
     * Invalidate OnSale label cache when a rule is deleted
     *
     * @param Varien_Event_Observer $observer
     * @return AW_Onsale_Model_Observer_Cache
     */
    public function onSaleRuleDeleteAfter($observer)
    {
        try {
            $rule = $observer->getEvent()->getRule();
            if ($rule && $rule->getId()) {
                // Clear all OnSale label cache when rules are deleted
                Mage::app()->getCache()->clean(
                    Zend_Cache::CLEANING_MODE_MATCHING_TAG,
                    array(self::CACHE_TAG)
                );
                
                Mage::log('OnSale label cache cleared after rule delete: ' . $rule->getId(), null, 'onsale_cache.log');
            }
        } catch (Exception $e) {
            Mage::logException($e);
        }
        
        return $this;
    }
    
    /**
     * Invalidate OnSale label cache when product attributes change
     * that could affect OnSale label display
     *
     * @param Varien_Event_Observer $observer
     * @return AW_Onsale_Model_Observer_Cache
     */
    public function catalogProductSaveAfter($observer)
    {
        try {
            $product = $observer->getEvent()->getProduct();
            if ($product && $product->getId()) {
                // Check if OnSale-related attributes changed
                $onsaleAttributes = array(
                    'price', 'special_price', 'special_from_date', 'special_to_date',
                    'aw_os_category_display', 'aw_os_category_position', 
                    'aw_os_category_image', 'aw_os_category_image_path', 'aw_os_category_text'
                );
                
                $shouldClearCache = false;
                foreach ($onsaleAttributes as $attribute) {
                    if ($product->dataHasChangedFor($attribute)) {
                        $shouldClearCache = true;
                        break;
                    }
                }
                
                if ($shouldClearCache) {
                    // Clear OnSale label cache for this product's categories
                    $categoryIds = $product->getCategoryIds();
                    if (!empty($categoryIds)) {
                        Mage::app()->getCache()->clean(
                            Zend_Cache::CLEANING_MODE_MATCHING_TAG,
                            array(self::CACHE_TAG)
                        );
                        
                        Mage::log('OnSale label cache cleared after product save: ' . $product->getId(), null, 'onsale_cache.log');
                    }
                }
            }
        } catch (Exception $e) {
            Mage::logException($e);
        }
        
        return $this;
    }
    
    /**
     * Clear OnSale label cache when cache is refreshed
     *
     * @param Varien_Event_Observer $observer
     * @return AW_Onsale_Model_Observer_Cache
     */
    public function applicationCleanCache($observer)
    {
        try {
            $tags = $observer->getEvent()->getTags();
            if (empty($tags) || in_array(self::CACHE_TAG, $tags) || in_array('CATALOG_PRODUCT', $tags)) {
                Mage::app()->getCache()->clean(
                    Zend_Cache::CLEANING_MODE_MATCHING_TAG,
                    array(self::CACHE_TAG)
                );
                
                Mage::log('OnSale label cache cleared during cache refresh', null, 'onsale_cache.log');
            }
        } catch (Exception $e) {
            Mage::logException($e);
        }
        
        return $this;
    }
}
