<?php

/**
 * <AUTHOR> Team
 * @copyright Copyright (c) 2022 Amasty (https://www.amasty.com)
 * @package Product Feed
 */
class Amasty_Feed_MainController extends Mage_Core_Controller_Front_Action
{
    /**
     * exclude file extension from name, and lookup profile
     */
    public function downloadAction()
    {
        $fileName = $this->_sanitizeFileName($this->getRequest()->getParam('file'));
        try {           
            $file = str_replace(array('.csv', '.xml', '.txt'), '', $fileName);
            $this->_download($file);
        } catch (Exception $e) {
            try {
                //compatibility with primary generated file names, versions before  3.3.4 - July 24, 2016
                $filePath = Mage::helper('amfeed')->getDownloadPath('feeds', $fileName);
                $this->_prepareDownloadResponse($fileName, array(
                    'value' => $filePath,
                    'type' => 'filename'
                ));
            } catch (Exception $e) {
                Mage::logException($e, null, 'amfeed.log');
                $this->_forward('noRoute');
            }
        }
    }

    /**
     * lookup profile by filename and download
     */
    // public function getAction()
    // {
    //     try
    //     {

    //         $fileName = $this->getRequest()->getParam('file'); 
    //        //  Mage::log("DEBUG: getAction() reached with file = {$fileName}", null, 'custom_debug.log', true);           
    //          $brand = $this->getRequest()->getParam('brand'); // e.g., "Casio"

    //         Mage::log("DEBUG: getAction() reached with file = {$fileName}, brand = {$brand}", null, 'custom_debug.log', true);
    //         $this->_download($fileName);
    //     } catch (Exception $e) {
    //         $this->_forward('noRoute');
    //     }
    // }

    public function getAction()
    {
        try {
            $fileName = $this->getRequest()->getParam('file');
            $brand = $this->getRequest()->getParam('brand');
           
            // Check if we need to filter by brand
            if ($brand) {
                $this->_filteredDownload($fileName, $brand);
            } else {
                $this->_download($fileName);
            }
        } catch (Exception $e) {
            Mage::log('Exception in getAction(): ' . $e->getMessage(), null, 'custom_debug.log', true);
            $this->_forward('noRoute');
        }
    }

    /**
     * @param $fileName
     * @return Mage_Core_Controller_Varien_Action
     */
    protected function _download($fileName)
{
    $feedDir = Mage::getBaseDir('media') . DS . 'amfeed' . DS . 'feeds';

    // Case-insensitive file matching
    $matches = array();
    $handle = scandir($feedDir);
    foreach ($handle as $entry) {
        if (stripos($entry, $fileName . '.xml') !== false) {
            $matches[] = $feedDir . DS . $entry;
        }
    }

    if (empty($matches)) {
        Mage::throwException("Feed file not found: " . $fileName);
    }

    $filePath = $matches[0];   
    // Load and modify XML
    $xml = simplexml_load_file($filePath);

    foreach ($xml->channel->item as $item) {
        $gElements = $item->children('g', true);
        if (isset($gElements->image_link)) {
            $link = (string)$gElements->image_link;
            if (stripos($link, 'http://') === 0) {
                $gElements->image_link[0] = preg_replace('/^http:\/\//i', 'https://', $link);
            }
        }
    }

    $output = $xml->asXML();

    $this->getResponse()
        ->setHeader('Content-Type', 'application/xml; charset=UTF-8')
        ->setHeader('Content-Disposition', 'attachment; filename="' . basename($filePath) . '"')
        ->setHeader('Content-Length', strlen($output))
        ->setBody($output);
}


    protected function _filteredDownload($fileName, $brand)
    {
        $feedDir = Mage::getBaseDir('media') . DS . 'amfeed' . DS . 'feeds';

        // Case-insensitive file search using scandir
        $matches = array();
        $handle = scandir($feedDir);
        foreach ($handle as $entry) {
            if (stripos($entry, $fileName . '.xml') !== false) {
                $matches[] = $feedDir . DS . $entry;
            }
        }

        if (empty($matches)) {
            Mage::throwException("Feed file not found for: " . $fileName);
        }

        $filePath = $matches[0];      
        $originalXml = simplexml_load_file($filePath);
        $filteredXml = new SimpleXMLElement('<?xml version="1.0" encoding="UTF-8"?><rss version="2.0" xmlns:g="http://base.google.com/ns/1.0"><channel></channel></rss>');

        // Copy channel metadata (excluding <item>)
        foreach ($originalXml->channel->children() as $child) {
            if ($child->getName() !== 'item') {
                $filteredXml->channel->addChild($child->getName(), (string)$child);
            }
        }

        // Filter and copy items
        foreach ($originalXml->channel->item as $item) {
            $itemBrand = (string)$item->children('g', true)->brand;

            if (strcasecmp(trim($itemBrand), $brand) === 0) {
                $newItem = $filteredXml->channel->addChild('item');

                // Copy g: elements (namespaced)
                foreach ($item->children('g', true) as $child) {
                    $childName = $child->getName();
                    $childValue = (string)$child;

                    // Optional: replace http with https in image_link
                    if ($childName === 'image_link' && stripos($childValue, 'http://') === 0) {
                        $childValue = preg_replace('/^http:\/\//i', 'https://', $childValue);
                    }

                    $newChild = $newItem->addChild("g:{$childName}", null, 'http://base.google.com/ns/1.0');
                    $node = dom_import_simplexml($newChild);
                    $owner = $node->ownerDocument;
                    $node->appendChild($owner->createCDATASection($childValue));
                }

                // Copy non-namespaced elements (title, link, description, etc.)
                foreach ($item->children() as $child) {
                    $childName = $child->getName();
                    $childValue = (string)$child;

                    $newChild = $newItem->addChild($childName);
                    $node = dom_import_simplexml($newChild);
                    $owner = $node->ownerDocument;
                    $node->appendChild($owner->createCDATASection($childValue));
                }
            }
        }

        $output = $filteredXml->asXML();
        $downloadFileName = basename($filePath, '.xml') . '_filtered_' . strtolower($brand) . '.xml';

        $this->getResponse()
            ->setHeader('Content-Type', 'application/xml; charset=UTF-8')
            ->setHeader('Content-Disposition', 'attachment; filename="' . $downloadFileName . '"')
            ->setHeader('Content-Length', strlen($output))
            ->setBody($output);
    }


    /**
     * @param $filename
     * @return mixed
     */
    protected static function _sanitizeFileName($filename)
    {
        $chars = array(" ", '"', "'", "&", "/", "\\", "?", "#");

        // every forbidden character is replace by an underscore
        return str_replace($chars, '_', $filename);
    }
}
