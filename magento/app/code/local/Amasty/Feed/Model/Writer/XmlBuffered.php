<?php
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) 2022 Amasty (https://www.amasty.com)
 * @package Product Feed
 * 
 * Optimized XML Writer with buffered writing for improved performance
 */
class Amasty_Feed_Model_Writer_XmlBuffered extends Amasty_Feed_Model_Writer_Buffered
{
    /**
     * XML item template cache
     * @var string
     */
    protected $_xmlItemTemplate;
    
    /**
     * XML header cache
     * @var string
     */
    protected $_xmlHeader;
    
    /**
     * XML footer cache
     * @var string
     */
    protected $_xmlFooter;
    
    /**
     * Compiled field templates for performance
     * @var array
     */
    protected $_compiledTemplates = [];
    
    /**
     * Initialize XML writer with cached templates
     * 
     * @param Amasty_Feed_Model_Profile $feed
     * @return $this
     */
    function init($feed)
    {
        parent::init($feed);
        
        // Cache XML templates for performance
        $this->_xmlItemTemplate = $this->_getFeed()->getXmlItem();
        $this->_xmlHeader = $this->_getFeed()->getXmlHeader();
        $this->_xmlFooter = $this->_getFeed()->getXmlFooter();
        
        // Pre-compile field templates
        $this->_compileFieldTemplates();
        
        return $this;
    }
    
    /**
     * Pre-compile field templates for faster processing
     * 
     * @return $this
     */
    protected function _compileFieldTemplates()
    {
        $lines2fields = $this->_getFeed()->getLines2fields();
        $fields = $this->_getFeed()->getFields();
        
        foreach ($lines2fields as $index => $lines2field) {
            $template = $lines2field['tpl'];
            $vars = isset($lines2field['vars']) ? $lines2field['vars'] : [];
            $links = isset($lines2field['links']) ? $lines2field['links'] : [];
            
            $this->_compiledTemplates[$index] = [
                'template' => $template,
                'vars' => $vars,
                'links' => $links,
                'optional_fields' => []
            ];
            
            // Pre-identify optional fields
            foreach ($links as $linkIndex => $link) {
                $isOptional = isset($fields['optional'][$link]) && ($fields['optional'][$link] == 'yes');
                $this->_compiledTemplates[$index]['optional_fields'][$linkIndex] = $isOptional;
            }
        }
        
        return $this;
    }
    
    /**
     * Write XML data with optimized buffering
     * 
     * @return $this
     */
    function write()
    {
        if ($this->isFirstStep()) {
            $this->_writeHeader();
        }
        
        $ret = parent::write();
        
        if ($this->isLastStep()) {
            $this->_writeFooter();
        }
        
        return $ret;
    }
    
    /**
     * Write XML header with date replacement
     * 
     * @return $this
     */
    protected function _writeHeader()
    {
        $header = str_replace(
            '<created_at>{{DATE}}</created_at>',
            '<created_at>' . date('Y-m-d H:i') . '</created_at>',
            $this->_xmlHeader
        );
        
        $this->writeToBuffer($header);
        
        return $this;
    }
    
    /**
     * Write XML footer
     * 
     * @return $this
     */
    protected function _writeFooter()
    {
        $this->writeToBuffer($this->_xmlFooter);
        
        return $this;
    }
    
    /**
     * Write a single XML record with optimized template processing
     * 
     * @param array $row
     * @return $this
     */
    public function writeRecord($row)
    {
        if (!is_array($row)) {
            return $this;
        }
        
        $xmlContent = $this->_buildXmlRecord($row);
        
        if (!empty($xmlContent)) {
            $xmlRecord = !empty($this->_xmlItemTemplate) 
                ? "<{$this->_xmlItemTemplate}>{$xmlContent}</{$this->_xmlItemTemplate}>"
                : $xmlContent;
                
            $this->writeToBuffer($xmlRecord);
        }
        
        return $this;
    }
    
    /**
     * Build XML record content using compiled templates
     * 
     * @param array $row
     * @return string
     */
    protected function _buildXmlRecord($row)
    {
        $xmlParts = [];
        
        foreach ($this->_compiledTemplates as $compiledTemplate) {
            $xmlPart = $this->_processCompiledTemplate($compiledTemplate, $row);
            
            if (!empty($xmlPart)) {
                $xmlParts[] = $xmlPart;
            }
        }
        
        return implode('', $xmlParts);
    }
    
    /**
     * Process a compiled template with row data
     * 
     * @param array $compiledTemplate
     * @param array $row
     * @return string
     */
    protected function _processCompiledTemplate($compiledTemplate, $row)
    {
        $template = $compiledTemplate['template'];
        $vars = $compiledTemplate['vars'];
        $links = $compiledTemplate['links'];
        $optionalFields = $compiledTemplate['optional_fields'];
        
        $hasRequiredContent = false;
        
        if (!empty($vars)) {
            foreach ($vars as $varIndex => $var) {
                $link = $links[$varIndex];
                $isOptional = $optionalFields[$varIndex];
                $value = isset($row[$link]) ? $row[$link] : '';
                
                // Check if we have required content
                if (!$isOptional && !empty($value)) {
                    $hasRequiredContent = true;
                }
                
                // Skip entire template if required field is empty
                if (!$isOptional && empty($value)) {
                    return '';
                }
                
                // Replace variable in template
                $template = str_replace('{' . $var . '}', $this->_escapeXmlValue($value), $template);
            }
        } else {
            $hasRequiredContent = true; // Static content
        }
        
        return $hasRequiredContent ? $template : '';
    }
    
    /**
     * Escape XML special characters for safe output
     * 
     * @param string $value
     * @return string
     */
    protected function _escapeXmlValue($value)
    {
        // Use htmlspecialchars for basic XML escaping
        return htmlspecialchars($value, ENT_XML1 | ENT_COMPAT, 'UTF-8');
    }
    
    /**
     * Estimate record size for XML format
     * 
     * @return int
     */
    protected function _estimateRecordSize()
    {
        $baseSize = parent::_estimateRecordSize();
        
        // XML has significant overhead due to tags
        $xmlOverhead = 2.0; // 100% overhead for XML tags and structure
        
        // Add item template overhead
        if (!empty($this->_xmlItemTemplate)) {
            $xmlOverhead += (strlen($this->_xmlItemTemplate) * 2 + 5) / $baseSize; // Opening and closing tags
        }
        
        return (int)($baseSize * $xmlOverhead);
    }
}
