<?xml version="1.0"?>
<config>
    <sections>
        <pfg translate="label" module="pfg_cleanurls">
            <label>PFG</label>
            <tab>advanced</tab>
            <frontend_type>text</frontend_type>
            <sort_order>1000</sort_order>
            <show_in_default>1</show_in_default>
            <show_in_website>1</show_in_website>
            <show_in_store>1</show_in_store>
            <groups>
                <cleanurls translate="label" module="pfg_cleanurls">
                    <label>Clean URLs</label>
                    <frontend_type>text</frontend_type>
                    <sort_order>100</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                    <fields>
                        <info translate="label comment">
                            <label>Clean URLs Management</label>
                            <comment><![CDATA[Go to <strong>PFG > Clean URLs</strong> in the admin menu to analyze and clean your URL rewrites.]]></comment>
                            <frontend_type>label</frontend_type>
                            <sort_order>5</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </info>
                        <enabled translate="label comment">
                            <label>Enable Clean URLs</label>
                            <comment>When enabled, products will only have one clean URL (no category-based URLs)</comment>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>10</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </enabled>
                        <clean_existing translate="label comment">
                            <label>Clean Existing URLs</label>
                            <comment>Remove existing category-based URLs when saving products</comment>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>20</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </clean_existing>
                        <requirement translate="label comment">
                            <label>Important Requirement</label>
                            <comment><![CDATA[This module requires "Use Categories Path for Product URLs" to be set to <strong>No</strong> in Catalog > Search Engine Optimizations.]]></comment>
                            <frontend_type>label</frontend_type>
                            <sort_order>30</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </requirement>
                    </fields>
                </cleanurls>
            </groups>
        </pfg>
    </sections>
</config>
