<?php
/**
 * Clean URLs Model - Override Magento's URL generation
 * Prevents creation of category-based product URLs
 */
class PFG_CleanUrls_Model_Catalog_Url extends Mage_Catalog_Model_Url
{
    /**
     * Override product rewrite refresh to only create clean URLs
     * 
     * @param Varien_Object $product
     * @param Varien_Object $category
     * @return PFG_CleanUrls_Model_Catalog_Url
     */
    protected function _refreshProductRewrite(Varien_Object $product, Varien_Object $category)
    {
        // Check if clean URLs are enabled
        if (!$this->_isCleanUrlsEnabled()) {
            return parent::_refreshProductRewrite($product, $category);
        }
        
        // Only create URL rewrite for root category (clean URL without category path)
        $rootCategory = $this->getStoreRootCategory($category->getStoreId());
        
        // Only process if this is the root category
        if ($category->getId() == $rootCategory->getId()) {
            return parent::_refreshProductRewrite($product, $category);
        }
        
        // For non-root categories, skip URL rewrite creation
        return $this;
    }
    
    /**
     * Override category product rewrites refresh
     * Only create clean product URLs, not category-based ones
     * 
     * @param Varien_Object $category
     * @return PFG_CleanUrls_Model_Catalog_Url
     */
    protected function _refreshCategoryProductRewrites(Varien_Object $category)
    {
        // Check if clean URLs are enabled
        if (!$this->_isCleanUrlsEnabled()) {
            return parent::_refreshCategoryProductRewrites($category);
        }
        
        $originalRewrites = $this->_rewrites;
        $process = true;
        $lastEntityId = 0;
        $firstIteration = true;
        
        while ($process == true) {
            $products = $this->getResource()->getProductsByCategory($category, $lastEntityId);
            if (!$products) {
                if ($firstIteration) {
                    // Clean up existing category-product rewrites if enabled
                    if ($this->_shouldCleanExisting()) {
                        $this->getResource()->deleteCategoryProductStoreRewrites(
                            $category->getId(),
                            array(),
                            $category->getStoreId()
                        );
                    }
                }
                $process = false;
                break;
            }

            // Only create rewrites for root category (clean URLs)
            $rootCategory = $this->getStoreRootCategory($category->getStoreId());
            $categoryIds = array($rootCategory->getId());
            
            $this->_rewrites = $this->getResource()->prepareRewrites(
                $category->getStoreId(),
                $categoryIds,
                array_keys($products)
            );

            foreach ($products as $product) {
                // Only create rewrite in root category (clean URL)
                $this->_refreshProductRewrite($product, $rootCategory);
            }
            
            $firstIteration = false;
            unset($products);
        }
        
        $this->_rewrites = $originalRewrites;
        return $this;
    }
    
    /**
     * Check if clean URLs are enabled
     * 
     * @return bool
     */
    protected function _isCleanUrlsEnabled()
    {
        return Mage::getStoreConfigFlag('pfg/cleanurls/enabled');
    }
    
    /**
     * Check if we should clean existing category-based URLs
     * 
     * @return bool
     */
    protected function _shouldCleanExisting()
    {
        return Mage::getStoreConfigFlag('pfg/cleanurls/clean_existing');
    }
    
    /**
     * Override to prevent category-based product URL generation
     * 
     * @param int $productId
     * @param int|null $storeId
     * @return PFG_CleanUrls_Model_Catalog_Url
     */
    public function refreshProductRewrite($productId, $storeId = null)
    {
        if (!$this->_isCleanUrlsEnabled()) {
            return parent::refreshProductRewrite($productId, $storeId);
        }
        
        if (is_null($storeId)) {
            foreach ($this->getStores() as $store) {
                $this->refreshProductRewrite($productId, $store->getId());
            }
            return $this;
        }

        // Clean existing category-based URLs if enabled
        if ($this->_shouldCleanExisting()) {
            $this->_cleanProductCategoryUrls($productId, $storeId);
        }
        
        // Load product
        $product = $this->getResource()->getProduct($productId, $storeId);
        if (!$product) {
            return $this;
        }

        // Only create clean URL (root category)
        $storeRootCategoryId = $this->getStores($storeId)->getRootCategoryId();
        $rootCategory = $this->getResource()->getCategory($storeRootCategoryId, $storeId);
        
        if ($rootCategory) {
            $this->_categories = array($storeRootCategoryId => $rootCategory);
            $this->_refreshProductRewrite($product, $rootCategory);
        }

        unset($product);
        $this->_categories = array();
        
        return $this;
    }
    
    /**
     * Clean category-based URLs for a specific product
     * 
     * @param int $productId
     * @param int $storeId
     */
    protected function _cleanProductCategoryUrls($productId, $storeId)
    {
        $adapter = $this->getResource()->getWriteAdapter();
        $table = $this->getResource()->getMainTable();
        
        // Delete all category-based URLs for this product
        $adapter->delete($table, array(
            'product_id = ?' => $productId,
            'store_id = ?' => $storeId,
            'category_id IS NOT NULL'
        ));
    }
}
