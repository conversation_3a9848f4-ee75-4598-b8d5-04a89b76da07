<?php
/**
 * Clean URLs Admin Block
 */
class PFG_CleanUrls_Block_Adminhtml_CleanUrls extends Mage_Adminhtml_Block_Widget_Container
{
    public function __construct()
    {
        parent::__construct();
        $this->_headerText = Mage::helper('pfg_cleanurls')->__('Clean URLs Management');
    }
    
    /**
     * Get analyze URL
     * 
     * @return string
     */
    public function getAnalyzeUrl()
    {
        return $this->getUrl('*/*/analyze');
    }
    
    /**
     * Get clean URL
     *
     * @return string
     */
    public function getCleanUrl()
    {
        return $this->getUrl('*/*/clean');
    }

    /**
     * Get backups URL
     *
     * @return string
     */
    public function getBackupsUrl()
    {
        return $this->getUrl('*/*/getBackups');
    }

    /**
     * Get revert URL
     *
     * @return string
     */
    public function getRevertUrl()
    {
        return $this->getUrl('*/*/revert');
    }

    /**
     * Get delete backup URL
     *
     * @return string
     */
    public function getDeleteBackupUrl()
    {
        return $this->getUrl('*/*/deleteBackup');
    }
    
    /**
     * Check if module can be used
     * 
     * @return bool
     */
    public function canUseModule()
    {
        return !Mage::getStoreConfigFlag('catalog/seo/product_use_categories');
    }
    
    /**
     * Get configuration URL
     * 
     * @return string
     */
    public function getConfigUrl()
    {
        return $this->getUrl('adminhtml/system_config/edit', array('section' => 'catalog'));
    }
}
