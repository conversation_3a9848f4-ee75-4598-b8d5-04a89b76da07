<?php
/**
 * PFG Analytics Helper
 * 
 * This helper class provides utility methods for the PFG Analytics module.
 * It handles configuration retrieval and common functionality.
 * 
 * @package PFG_Analytics
 * <AUTHOR> Development Team
 */

class PFG_Analytics_Helper_Data extends Mage_Core_Helper_Abstract
{
    /**
     * Configuration paths
     */
    const XML_PATH_ENABLED = 'pfg_analytics/general/enabled';
    const XML_PATH_STATUS_MAPPINGS = 'pfg_analytics/status_mapping/mappings';

    /**
     * Default values and constants
     */
    const DEFAULT_PRIORITY = 999;
    const DEFAULT_DATE_RANGE_DAYS = 90;
    const MAX_CHART_DAYS = 365;
    const CACHE_LIFETIME = 3600; // 1 hour
    const CACHE_TAG = 'PFG_ANALYTICS';

    /**
     * Date formats
     */
    const DATE_FORMAT_INPUT = 'Y-m-d';
    const DATE_FORMAT_DISPLAY = 'M j, Y';
    const DATETIME_FORMAT_START = 'Y-m-d 00:00:00';
    const DATETIME_FORMAT_END = 'Y-m-d 23:59:59';

    /**
     * Chart configuration
     */
    const CHART_GROUPING_DAY = 'day';
    const CHART_GROUPING_WEEK = 'week';
    const CHART_GROUPING_MONTH = 'month';

    /**
     * Check if the module is enabled
     *
     * @param mixed $store Store ID or store model
     * @return bool True if module is enabled, false otherwise
     */
    public function isEnabled($store = null)
    {
        return Mage::getStoreConfigFlag(self::XML_PATH_ENABLED, $store);
    }

    /**
     * Validate date format
     *
     * @param string $date Date string to validate
     * @return bool True if valid, false otherwise
     */
    public function validateDate($date)
    {
        $d = DateTime::createFromFormat(self::DATE_FORMAT_INPUT, $date);
        return $d && $d->format(self::DATE_FORMAT_INPUT) === $date;
    }

    /**
     * Get cache key for analytics data
     *
     * @param array $params Parameters to include in cache key
     * @return string Cache key
     */
    public function getCacheKey($params = array())
    {
        // PERFORMANCE OPTIMIZATION: Use json_encode instead of serialize for better performance
        return self::CACHE_TAG . '_' . md5(json_encode($params));
    }

    /**
     * Get status mappings configuration
     *
     * @param mixed $store Store ID or store model
     * @return array Status mappings array
     */
    public function getStatusMappings($store = null)
    {
        try {
            $mappings = Mage::getStoreConfig(self::XML_PATH_STATUS_MAPPINGS, $store);
            if ($mappings) {
                $unserializedMappings = @unserialize($mappings);
                if ($unserializedMappings !== false && is_array($unserializedMappings)) {
                    // Sort by priority (ascending order - lower numbers first)
                    uasort($unserializedMappings, function($a, $b) {
                        $priorityA = isset($a['priority']) ? (int)$a['priority'] : self::DEFAULT_PRIORITY;
                        $priorityB = isset($b['priority']) ? (int)$b['priority'] : self::DEFAULT_PRIORITY;
                        return $priorityA - $priorityB;
                    });
                    return $unserializedMappings;
                }
            }
        } catch (Exception $e) {
            Mage::logException($e);
        }
        return array();
    }



    /**
     * Get all available order statuses
     *
     * @return array Array of order statuses with labels
     */
    public function getOrderStatuses()
    {
        try {
            return Mage::getModel('sales/order_status')->getResourceCollection()->toOptionHash();
        } catch (Exception $e) {
            Mage::logException($e);
            return array();
        }
    }

    /**
     * Format currency amount
     *
     * @param float $amount Amount to format
     * @param mixed $store Store ID or store model
     * @return string Formatted currency string
     */
    public function formatCurrency($amount, $store = null)
    {
        try {
            return Mage::helper('core')->currency($amount, true, false);
        } catch (Exception $e) {
            Mage::logException($e);
            return number_format($amount, 2);
        }
    }

    /**
     * Get analytics model instance
     *
     * @return PFG_Analytics_Model_Analytics
     */
    public function getAnalyticsModel()
    {
        return Mage::getModel('pfg_analytics/analytics');
    }

    /**
     * Validate and sanitize date range
     *
     * @param string $fromDate Start date
     * @param string $toDate End date
     * @return array Validated date range or error
     */
    public function validateDateRange($fromDate, $toDate)
    {
        $errors = array();

        // Validate date formats
        if (!$this->validateDate($fromDate)) {
            $errors[] = $this->__('Invalid start date format. Use YYYY-MM-DD.');
        }

        if (!$this->validateDate($toDate)) {
            $errors[] = $this->__('Invalid end date format. Use YYYY-MM-DD.');
        }

        if (empty($errors)) {
            // Check date logic
            $from = new DateTime($fromDate);
            $to = new DateTime($toDate);

            if ($from > $to) {
                $errors[] = $this->__('Start date cannot be after end date.');
            }

            // Check maximum range
            $diff = $from->diff($to);
            if ($diff->days > self::MAX_CHART_DAYS) {
                $errors[] = $this->__('Date range cannot exceed %d days.', self::MAX_CHART_DAYS);
            }
        }

        return array(
            'valid' => empty($errors),
            'errors' => $errors,
            'from_date' => $fromDate,
            'to_date' => $toDate
        );
    }

    /**
     * Sanitize status array input
     *
     * @param mixed $statuses Status input (string, array, or null)
     * @return array Sanitized status array
     */
    public function sanitizeStatuses($statuses)
    {
        if (empty($statuses)) {
            return array();
        }

        if (!is_array($statuses)) {
            $statuses = array($statuses);
        }

        // Remove empty values and sanitize
        $sanitized = array();
        foreach ($statuses as $status) {
            $status = trim($status);
            if (!empty($status)) {
                $sanitized[] = $status;
            }
        }

        return $sanitized;
    }
}
