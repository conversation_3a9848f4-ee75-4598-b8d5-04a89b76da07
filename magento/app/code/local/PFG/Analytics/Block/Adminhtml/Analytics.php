<?php
/**
 * PFG Analytics Admin Block
 *
 * This block class handles the rendering of the main Analytics page
 * in the admin panel. It provides sales analytics and reporting features.
 *
 * @package PFG_Analytics
 * <AUTHOR> Development Team
 */

class PFG_Analytics_Block_Adminhtml_Analytics extends Mage_Adminhtml_Block_Widget_Container
{
    /**
     * Constructor
     *
     * Initialize the block and set up the basic configuration
     */
    public function __construct()
    {
        parent::__construct();
        
        // Set the block template
        $this->setTemplate('pfg/analytics/index.phtml');
        
        // Set the header text for the page
        $this->_headerText = Mage::helper('pfg_analytics')->__('PFG Analytics');
    }

    /**
     * Prepare the layout
     *
     * @return PFG_Analytics_Block_Adminhtml_Analytics
     */
    protected function _prepareLayout()
    {
        parent::_prepareLayout();
        
        // Set the page title
        if ($headBlock = $this->getLayout()->getBlock('head')) {
            $headBlock->setTitle(
                Mage::helper('pfg_analytics')->__('PFG Analytics')
            );
        }
        
        return $this;
    }

    /**
     * Get the filter form action URL
     *
     * @return string URL for the filter form submission
     */
    public function getFilterUrl()
    {
        return $this->getUrl('*/*/index');
    }

    /**
     * Get the from date for filtering
     *
     * @return string From date in Y-m-d format
     */
    public function getFromDate()
    {
        $fromDate = $this->getRequest()->getParam('from_date');
        if (!$fromDate || !Mage::helper('pfg_analytics')->validateDate($fromDate)) {
            // Default to 90 days ago
            $fromDate = date(PFG_Analytics_Helper_Data::DATE_FORMAT_INPUT, strtotime('-' . PFG_Analytics_Helper_Data::DEFAULT_DATE_RANGE_DAYS . ' days'));
        }
        return $fromDate;
    }

    /**
     * Get the to date for filtering
     *
     * @return string To date in Y-m-d format
     */
    public function getToDate()
    {
        $toDate = $this->getRequest()->getParam('to_date');
        if (!$toDate || !Mage::helper('pfg_analytics')->validateDate($toDate)) {
            // Default to today
            $toDate = date(PFG_Analytics_Helper_Data::DATE_FORMAT_INPUT);
        }
        return $toDate;
    }

    /**
     * Get selected order statuses for filtering
     *
     * @return array Array of selected order statuses
     */
    public function getSelectedStatuses()
    {
        $statuses = $this->getRequest()->getParam('order_statuses');
        
        if (!$statuses || (is_array($statuses) && count($statuses) == 1 && empty($statuses[0]))) {
            return array(); // Empty array means all statuses
        }
        if (!is_array($statuses)) {
            return array($statuses);
        }
        
        return $statuses;
    }

    /**
     * Get comparison mode
     *
     * @return string Comparison mode
     */
    public function getComparisonMode()
    {
        return $this->getRequest()->getParam('comparison_mode', 'none');
    }

    /**
     * Get quick filter period
     *
     * @return string Quick filter period
     */
    public function getQuickFilter()
    {
        return $this->getRequest()->getParam('quick_filter', '');
    }

    /**
     * Get all available order statuses
     *
     * @return array Array of order statuses with labels
     */
    public function getOrderStatuses()
    {
        return Mage::helper('pfg_analytics')->getOrderStatuses();
    }

    /**
     * Format currency amount
     *
     * @param float $amount Amount to format
     * @return string Formatted currency string
     */
    public function formatCurrency($amount)
    {
        return Mage::helper('pfg_analytics')->formatCurrency($amount);
    }

    /**
     * Get current date and time
     *
     * @return string Formatted current date and time
     */
    public function getCurrentDateTime()
    {
        return Mage::getModel('core/date')->date('Y-m-d H:i:s');
    }

    /**
     * Check if user has permission to view detailed reports
     *
     * @return bool True if user has permission, false otherwise
     */
    public function canViewDetailedReports()
    {
        return Mage::getSingleton('admin/session')->isAllowed('pfg/analytics');
    }

    /**
     * Get the calendar date format for JavaScript
     *
     * @return string Date format string
     */
    public function getDateFormat()
    {
        return 'yyyy-MM-dd';
    }

    /**
     * Get available comparison modes
     *
     * @return array Available comparison modes
     */
    public function getComparisonModes()
    {
        return array(
            'none' => $this->__('No Comparison'),
            'previous_year' => $this->__('Same Period Previous Year'),
            'previous_period' => $this->__('Previous Period')
        );
    }

    /**
     * Get available chart grouping options
     *
     * @return array Available chart grouping options
     */
    public function getChartGroupingOptions()
    {
        return array(
            'day' => $this->__('Per Day'),
            'week' => $this->__('Per Week'),
            'month' => $this->__('Per Month')
        );
    }

    /**
     * Get current chart grouping
     *
     * @return string Current chart grouping
     */
    public function getChartGrouping()
    {
        return $this->getRequest()->getParam('chart_grouping', 'day');
    }

    /**
     * Get comparison from date
     *
     * @return string Comparison from date
     */
    public function getComparisonFromDate()
    {
        return $this->getRequest()->getParam('comparison_from_date', '');
    }

    /**
     * Get comparison to date
     *
     * @return string Comparison to date
     */
    public function getComparisonToDate()
    {
        return $this->getRequest()->getParam('comparison_to_date', '');
    }

    /**
     * Get status mappings configuration
     *
     * @return array Status mappings
     */
    public function getStatusMappings()
    {
        return Mage::getModel('pfg_analytics/config')->getStatusMappings();
    }

    /**
     * Get sales model instance
     *
     * @return PFG_Analytics_Model_Sales
     */
    public function getSalesModel()
    {
        return Mage::getModel('pfg_analytics/sales');
    }

    /**
     * Get configuration model instance
     *
     * @return PFG_Analytics_Model_Config
     */
    public function getConfigModel()
    {
        return Mage::getModel('pfg_analytics/config');
    }

    /**
     * Get sales data for the specified date range with caching
     *
     * @return array Sales data including count, total, average, and status breakdown
     */
    public function getSalesData()
    {
        $fromDate = $this->getFromDate();
        $toDate = $this->getToDate();
        $selectedStatuses = $this->getSelectedStatuses();
        $comparisonMode = $this->getComparisonMode();

        // Create cache key
        $cacheKey = Mage::helper('pfg_analytics')->getCacheKey(array(
            'sales_data',
            $fromDate,
            $toDate,
            $selectedStatuses,
            $comparisonMode
        ));

        // Try to get from cache first
        $cache = Mage::app()->getCache();
        if ($cachedData = $cache->load($cacheKey)) {
            return unserialize($cachedData);
        }

        try {
            $analyticsModel = Mage::helper('pfg_analytics')->getAnalyticsModel();

            // Get primary period data using optimized model
            $primaryData = $analyticsModel->getSalesDataForPeriod($fromDate, $toDate, $selectedStatuses);
            $primaryData['from_date'] = $fromDate;
            $primaryData['to_date'] = $toDate;
            $primaryData['selected_statuses'] = $selectedStatuses;

            // Get comparison data if comparison mode is enabled
            $comparisonData = null;
            if ($comparisonMode !== 'none') {
                $comparisonDates = $this->getComparisonDates($fromDate, $toDate, $comparisonMode);
                if ($comparisonDates) {
                    $comparisonData = $analyticsModel->getSalesDataForPeriod(
                        $comparisonDates['from'],
                        $comparisonDates['to'],
                        $selectedStatuses
                    );
                    $comparisonData['from_date'] = $comparisonDates['from'];
                    $comparisonData['to_date'] = $comparisonDates['to'];
                }
            }

            // Add comparison data and percentage changes to primary data
            if ($comparisonData) {
                $primaryData['comparison'] = $comparisonData;
                $primaryData['comparison_mode'] = $comparisonMode;

                // Calculate percentage changes using model method
                $primaryData['total_orders_change'] = $analyticsModel->calculatePercentageChange(
                    $comparisonData['total_orders'],
                    $primaryData['total_orders']
                );
                $primaryData['total_amount_change'] = $analyticsModel->calculatePercentageChange(
                    $comparisonData['total_amount'],
                    $primaryData['total_amount']
                );
                $primaryData['average_amount_change'] = $analyticsModel->calculatePercentageChange(
                    $comparisonData['average_amount'],
                    $primaryData['average_amount']
                );
            }

            // Cache the result for 1 hour
            $cache->save(
                serialize($primaryData),
                $cacheKey,
                array(PFG_Analytics_Helper_Data::CACHE_TAG),
                PFG_Analytics_Helper_Data::CACHE_LIFETIME
            );

            return $primaryData;

        } catch (Exception $e) {
            Mage::logException($e);
            return array(
                'total_orders' => 0,
                'total_amount' => 0,
                'average_amount' => 0,
                'status_breakdown' => array(),
                'error' => $this->__('Unable to load sales data. Please try again.')
            );
        }
    }





    /**
     * Get chart data for purchases with caching
     *
     * @return array Chart data with dates and order counts
     */
    public function getChartData()
    {
        $fromDate = $this->getFromDate();
        $toDate = $this->getToDate();
        $selectedStatuses = $this->getSelectedStatuses();
        $comparisonMode = $this->getComparisonMode();
        $grouping = $this->getChartGrouping();

        // Create cache key
        $cacheKey = Mage::helper('pfg_analytics')->getCacheKey(array(
            'chart_data',
            $fromDate,
            $toDate,
            $selectedStatuses,
            $comparisonMode,
            $grouping
        ));

        // Try to get from cache first
        $cache = Mage::app()->getCache();
        if ($cachedData = $cache->load($cacheKey)) {
            return unserialize($cachedData);
        }

        try {
            $analyticsModel = Mage::helper('pfg_analytics')->getAnalyticsModel();

            // Get primary period data using optimized model
            $primaryData = $analyticsModel->getChartDataForPeriod($fromDate, $toDate, $selectedStatuses, $grouping);

            // Get comparison data if comparison mode is enabled
            $comparisonData = null;
            if ($comparisonMode !== 'none') {
                $comparisonDates = $this->getComparisonDates($fromDate, $toDate, $comparisonMode);
                if ($comparisonDates) {
                    $comparisonData = $analyticsModel->getChartDataForPeriod(
                        $comparisonDates['from'],
                        $comparisonDates['to'],
                        $selectedStatuses,
                        $grouping
                    );
                }
            }

            $result = array(
                'primary' => $primaryData,
                'comparison' => $comparisonData,
                'comparison_mode' => $comparisonMode,
                'selected_statuses' => $selectedStatuses,
                'grouping' => $grouping
            );

            // Cache the result for 1 hour
            $cache->save(
                serialize($result),
                $cacheKey,
                array(PFG_Analytics_Helper_Data::CACHE_TAG),
                PFG_Analytics_Helper_Data::CACHE_LIFETIME
            );

            return $result;

        } catch (Exception $e) {
            Mage::logException($e);
            return array(
                'primary' => array('labels' => array(), 'data' => array()),
                'comparison' => null,
                'comparison_mode' => $comparisonMode,
                'selected_statuses' => $selectedStatuses,
                'grouping' => $grouping,
                'error' => $this->__('Unable to load chart data. Please try again.')
            );
        }
    }



    /**
     * Get chart data as JSON for JavaScript
     *
     * @return string JSON encoded chart data
     */
    public function getChartDataJson()
    {
        return Mage::helper('core')->jsonEncode($this->getChartData());
    }

    /**
     * Check if date range is valid for chart (max 1 year)
     *
     * @return bool True if valid, false otherwise
     */
    public function isValidDateRange()
    {
        $fromDate = $this->getFromDate();
        $toDate = $this->getToDate();

        $fromDateTime = new DateTime($fromDate);
        $toDateTime = new DateTime($toDate);
        $interval = $fromDateTime->diff($toDateTime);

        return $interval->days <= 365;
    }

    /**
     * Get maximum allowed date range message
     *
     * @return string Warning message for date range
     */
    public function getDateRangeWarning()
    {
        if (!$this->isValidDateRange()) {
            return $this->__('Date range limited to 1 year maximum. Showing data for 1 year from the start date.');
        }
        return '';
    }

    /**
     * Get comparison dates based on comparison mode
     *
     * @param string $fromDate Primary from date
     * @param string $toDate Primary to date
     * @param string $comparisonMode Comparison mode
     * @return array|null Comparison dates array or null
     */
    protected function getComparisonDates($fromDate, $toDate, $comparisonMode)
    {
        // Handle custom comparison mode
        if ($comparisonMode === 'custom') {
            $customFromDate = $this->getComparisonFromDate();
            $customToDate = $this->getComparisonToDate();

            if ($customFromDate && $customToDate) {
                return array(
                    'from' => $customFromDate,
                    'to' => $customToDate
                );
            }
            return null;
        }

        $fromDateTime = new DateTime($fromDate);
        $toDateTime = new DateTime($toDate);
        $interval = $fromDateTime->diff($toDateTime);

        switch ($comparisonMode) {
            case 'previous_year':
                // Same period previous year
                $compFromDate = clone $fromDateTime;
                $compFromDate->sub(new DateInterval('P1Y'));
                $compToDate = clone $toDateTime;
                $compToDate->sub(new DateInterval('P1Y'));
                break;

            case 'previous_period':
                // Previous period of same duration
                $periodDays = $interval->days + 1; // +1 to include both start and end dates
                $compToDate = clone $fromDateTime;
                $compToDate->sub(new DateInterval('P1D')); // End date is one day before current start
                $compFromDate = clone $compToDate;
                $compFromDate->sub(new DateInterval('P' . ($periodDays - 1) . 'D')); // Calculate start date
                break;

            default:
                return null;
        }

        return array(
            'from' => $compFromDate->format('Y-m-d'),
            'to' => $compToDate->format('Y-m-d')
        );
    }

    /**
     * Get quick filter dates
     *
     * @param string $filter Quick filter type
     * @return array Date range for the filter
     */
    public function getQuickFilterDates($filter)
    {
        $today = new DateTime();

        switch ($filter) {
            case '30days':
                $fromDate = clone $today;
                $fromDate->sub(new DateInterval('P30D'));
                return array(
                    'from' => $fromDate->format('Y-m-d'),
                    'to' => $today->format('Y-m-d')
                );

            case '90days':
                $fromDate = clone $today;
                $fromDate->sub(new DateInterval('P90D'));
                return array(
                    'from' => $fromDate->format('Y-m-d'),
                    'to' => $today->format('Y-m-d')
                );

            case 'ytd':
                return array(
                    'from' => date('Y') . '-01-01',
                    'to' => $today->format('Y-m-d')
                );

            case 'q1':
                return array(
                    'from' => date('Y') . '-01-01',
                    'to' => date('Y') . '-03-31'
                );

            case 'q2':
                return array(
                    'from' => date('Y') . '-04-01',
                    'to' => date('Y') . '-06-30'
                );

            case 'q3':
                return array(
                    'from' => date('Y') . '-07-01',
                    'to' => date('Y') . '-09-30'
                );

            case 'q4':
                return array(
                    'from' => date('Y') . '-10-01',
                    'to' => date('Y') . '-12-31'
                );

            default:
                return array(
                    'from' => $this->getFromDate(),
                    'to' => $this->getToDate()
                );
        }
    }

    /**
     * Get mapped status breakdown
     *
     * @param array $statusBreakdown Original status breakdown
     * @return array Mapped status breakdown
     */
    public function getMappedStatusBreakdown($statusBreakdown)
    {
        return $this->getConfigModel()->getGroupedStatusBreakdown($statusBreakdown);
    }

    /**
     * Get top selling categories data
     *
     * Retrieves and caches the top selling categories data for the current
     * date range and status filters. Uses the same filtering logic as other
     * analytics sections for consistency.
     *
     * @return array Top selling categories data with caching
     */
    public function getTopSellingCategoriesData()
    {
        $fromDate = $this->getFromDate();
        $toDate = $this->getToDate();
        $selectedStatuses = $this->getSelectedStatuses();

        // Create cache key
        $cacheKey = Mage::helper('pfg_analytics')->getCacheKey(array(
            'top_categories',
            $fromDate,
            $toDate,
            implode(',', $selectedStatuses)
        ));

        // Try to get from cache first
        $cache = Mage::app()->getCache();
        $cachedData = $cache->load($cacheKey);

        if ($cachedData !== false) {
            return unserialize($cachedData);
        }

        try {
            // Get data from model
            $analyticsModel = Mage::getModel('pfg_analytics/analytics');
            $data = $analyticsModel->getTopSellingCategoriesData(
                $fromDate,
                $toDate,
                $selectedStatuses,
                10 // Limit to top 10 categories
            );

            // Calculate percentages and add total row
            $data = $this->_addPercentagesAndTotal($data);

            // Cache the result
            $cache->save(
                serialize($data),
                $cacheKey,
                array(Mage::getModel('pfg_analytics/analytics')::CACHE_TAG),
                Mage::getModel('pfg_analytics/analytics')::CACHE_LIFETIME
            );

            return $data;

        } catch (Exception $e) {
            Mage::logException($e);
            return array();
        }
    }

    /**
     * Get top selling brands data with caching
     *
     * @return array
     */
    public function getTopSellingBrandsData()
    {
        try {
            // Get filter parameters
            $fromDate = $this->getRequest()->getParam('from_date');
            $toDate = $this->getRequest()->getParam('to_date');
            $selectedStatuses = $this->getRequest()->getParam('selected_statuses', array());

            // Set default date range if not provided
            if (empty($fromDate) || empty($toDate)) {
                $toDate = date('Y-m-d');
                $fromDate = date('Y-m-d', strtotime('-90 days'));
            }

            // Create cache key
            $cacheKey = 'pfg_analytics_top_brands_' . md5($fromDate . '_' . $toDate . '_' . implode(',', $selectedStatuses));

            // Try to get from cache first
            $cache = Mage::app()->getCache();
            $cachedData = $cache->load($cacheKey);

            if ($cachedData !== false) {
                return unserialize($cachedData);
            }

            // Get data from model
            $analyticsModel = Mage::getModel('pfg_analytics/analytics');
            $data = $analyticsModel->getTopSellingBrandsData(
                $fromDate,
                $toDate,
                $selectedStatuses,
                10 // Limit to top 10 brands
            );

            // Calculate percentages and add total row
            $data = $this->_addPercentagesAndTotal($data);

            // Cache the result
            $cache->save(
                serialize($data),
                $cacheKey,
                array(Mage::getModel('pfg_analytics/analytics')::CACHE_TAG),
                Mage::getModel('pfg_analytics/analytics')::CACHE_LIFETIME
            );

            return $data;

        } catch (Exception $e) {
            Mage::logException($e);
            return array();
        }
    }

    /**
     * Get top selling products data with caching
     *
     * @return array
     */
    public function getTopSellingProductsData()
    {
        try {
            // Get filter parameters
            $fromDate = $this->getRequest()->getParam('from_date');
            $toDate = $this->getRequest()->getParam('to_date');
            $selectedStatuses = $this->getRequest()->getParam('selected_statuses', array());

            // Set default date range if not provided
            if (empty($fromDate) || empty($toDate)) {
                $toDate = date('Y-m-d');
                $fromDate = date('Y-m-d', strtotime('-90 days'));
            }

            // Create cache key
            $cacheKey = 'pfg_analytics_top_products_' . md5($fromDate . '_' . $toDate . '_' . implode(',', $selectedStatuses));

            // Try to get from cache first
            $cache = Mage::app()->getCache();
            $cachedData = $cache->load($cacheKey);

            if ($cachedData !== false) {
                return unserialize($cachedData);
            }

            // Get data from model
            $analyticsModel = Mage::getModel('pfg_analytics/analytics');
            $data = $analyticsModel->getTopSellingProductsData(
                $fromDate,
                $toDate,
                $selectedStatuses,
                10 // Limit to top 10 products
            );

            // Calculate percentages and add total row
            $data = $this->_addPercentagesAndTotal($data);

            // Cache the result
            $cache->save(
                serialize($data),
                $cacheKey,
                array(Mage::getModel('pfg_analytics/analytics')::CACHE_TAG),
                Mage::getModel('pfg_analytics/analytics')::CACHE_LIFETIME
            );

            return $data;

        } catch (Exception $e) {
            Mage::logException($e);
            return array();
        }
    }

    /**
     * Add percentage calculations and total row to analytics data
     *
     * @param array $data Raw analytics data from model
     * @return array Enhanced data with percentages and total row
     */
    protected function _addPercentagesAndTotal($data)
    {
        if (empty($data) || !is_array($data)) {
            return $data;
        }

        // Calculate total quantity across all items
        $totalQuantity = 0;
        $totalOrders = 0;

        foreach ($data as $item) {
            if (isset($item['total_quantity'])) {
                $totalQuantity += (float)$item['total_quantity'];
            }
            if (isset($item['order_count'])) {
                $totalOrders += (int)$item['order_count'];
            }
        }

        // Add percentage to each item and ensure product_display field exists
        foreach ($data as &$item) {
            if (isset($item['total_quantity']) && $totalQuantity > 0) {
                $percentage = ((float)$item['total_quantity'] / $totalQuantity) * 100;
                $item['percentage'] = number_format($percentage, 1);
            } else {
                $item['percentage'] = '0.0';
            }

            // Ensure product_display field exists for products (copy from display_name)
            if (isset($item['display_name']) && !isset($item['product_display'])) {
                $item['product_display'] = $item['display_name'];
            }
        }

        // Add total row at the end
        $totalRow = array(
            'is_total_row' => true,
            'total_quantity' => $totalQuantity,
            'order_count' => $totalOrders,
            'percentage' => '100.0'
        );

        // Set appropriate display names based on data structure
        if (isset($data[0]['category_name'])) {
            $totalRow['category_name'] = 'Total';
        } elseif (isset($data[0]['brand_name'])) {
            $totalRow['brand_name'] = 'Total';
        } elseif (isset($data[0]['display_name'])) {
            $totalRow['display_name'] = 'Total';
            $totalRow['product_display'] = 'Total';
        }

        $data[] = $totalRow;

        return $data;
    }
}
