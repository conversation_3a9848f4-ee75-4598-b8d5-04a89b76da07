<?php
/**
 * PFG Analytics Status Mapping Configuration Field
 *
 * This block creates a custom configuration field for status mapping
 * in the system configuration area.
 *
 * @package PFG_Analytics
 * <AUTHOR> Development Team
 */

class PFG_Analytics_Block_Adminhtml_System_Config_Form_Field_Statusmapping extends Mage_Adminhtml_Block_System_Config_Form_Field_Array_Abstract
{
    /**
     * Status select renderer
     *
     * @var PFG_Analytics_Block_Adminhtml_System_Config_Form_Field_Statusselect
     */
    protected $_statusRenderer;

    /**
     * Prepare to render
     */
    protected function _prepareToRender()
    {
        $this->addColumn('priority', array(
            'label' => Mage::helper('pfg_analytics')->__('Priority'),
            'style' => 'width:80px',
            'class' => 'validate-number validate-greater-than-zero',
        ));

        $this->addColumn('custom_name', array(
            'label' => Mage::helper('pfg_analytics')->__('Custom Status Name'),
            'style' => 'width:200px',
        ));

        $this->addColumn('real_statuses', array(
            'label' => Mage::helper('pfg_analytics')->__('Real Order Statuses'),
            'renderer' => $this->_getStatusRenderer(),
            'style' => 'width:300px',
        ));

        $this->_addAfter = false;
        $this->_addButtonLabel = Mage::helper('pfg_analytics')->__('Add Status Mapping');
    }

    /**
     * Get status select renderer
     *
     * @return PFG_Analytics_Block_Adminhtml_System_Config_Form_Field_Statusselect
     */
    protected function _getStatusRenderer()
    {
        if (!$this->_statusRenderer) {
            $this->_statusRenderer = $this->getLayout()->createBlock(
                'pfg_analytics/adminhtml_system_config_form_field_statusselect',
                '',
                array('is_render_to_js_template' => true)
            );
        }
        return $this->_statusRenderer;
    }

    /**
     * Prepare existing row data object
     *
     * @param Varien_Object $row
     */
    protected function _prepareArrayRow(Varien_Object $row)
    {
        $realStatuses = $row->getData('real_statuses');
        if (!empty($realStatuses)) {
            // Handle both single values and arrays
            if (!is_array($realStatuses)) {
                $realStatuses = array($realStatuses);
            }

            // Set selected attribute for each status
            foreach ($realStatuses as $status) {
                $row->setData(
                    'option_extra_attr_' . $this->_getStatusRenderer()->calcOptionHash($status),
                    'selected="selected"'
                );
            }
        }
    }

    /**
     * Render array cell for prototypeJS template
     *
     * @param string $columnName
     * @return string
     */
    public function renderCellTemplate($columnName)
    {
        if ($columnName == 'real_statuses' && isset($this->_columns[$columnName])) {
            return $this->_columns[$columnName]['renderer']->setInputName(
                $this->getElement()->getName() . '[#{_id}][' . $columnName . '][]'
            )->setInputId(
                $this->getElement()->getId() . '_#{_id}_' . $columnName
            )->toHtml();
        }

        return parent::renderCellTemplate($columnName);
    }
}
