<?php
/**
 * PFG Analytics Status Mapping Configuration Field
 *
 * This block creates a custom configuration field for status mapping
 * in the system configuration area.
 *
 * @package PFG_Analytics
 * <AUTHOR> Development Team
 */

class PFG_Analytics_Block_Adminhtml_System_Config_Form_Field_Statusmapping extends Mage_Adminhtml_Block_System_Config_Form_Field_Array_Abstract
{
    /**
     * Status select renderer
     *
     * @var PFG_Analytics_Block_Adminhtml_System_Config_Form_Field_Statusselect
     */
    protected $_statusRenderer;

    /**
     * Prepare to render
     */
    protected function _prepareToRender()
    {
        $this->addColumn('priority', array(
            'label' => Mage::helper('pfg_analytics')->__('Priority'),
            'style' => 'width:80px',
            'class' => 'validate-number validate-greater-than-zero',
        ));

        $this->addColumn('custom_name', array(
            'label' => Mage::helper('pfg_analytics')->__('Custom Status Name'),
            'style' => 'width:100px',
        ));

        $this->addColumn('real_statuses', array(
            'label' => Mage::helper('pfg_analytics')->__('Real Order Statuses'),
            'renderer' => $this->_getStatusRenderer(),
            'style' => 'width:300px',
        ));

        $this->_addAfter = false;
        $this->_addButtonLabel = Mage::helper('pfg_analytics')->__('Add Status Mapping');
    }

    /**
     * Get status select renderer
     *
     * @return PFG_Analytics_Block_Adminhtml_System_Config_Form_Field_Statusselect
     */
    protected function _getStatusRenderer()
    {
        if (!$this->_statusRenderer) {
            $this->_statusRenderer = $this->getLayout()->createBlock(
                'pfg_analytics/adminhtml_system_config_form_field_statusselect',
                '',
                array('is_render_to_js_template' => true)
            );
        }
        return $this->_statusRenderer;
    }

    /**
     * Prepare existing row data object
     *
     * @param Varien_Object $row
     */
    protected function _prepareArrayRow(Varien_Object $row)
    {
        $realStatuses = $row->getData('real_statuses');
        if (!empty($realStatuses)) {
            // Handle both single values and arrays
            if (!is_array($realStatuses)) {
                $realStatuses = array($realStatuses);
            }

            // Set selected attribute for each status
            foreach ($realStatuses as $status) {
                $row->setData(
                    'option_extra_attr_' . $this->_getStatusRenderer()->calcOptionHash($status),
                    'selected="selected"'
                );
            }
        }
    }

    /**
     * Render array cell for prototypeJS template
     *
     * @param string $columnName
     * @return string
     */
    public function renderCellTemplate($columnName)
    {
        if ($columnName == 'real_statuses' && isset($this->_columns[$columnName])) {
            return $this->_columns[$columnName]['renderer']->setInputName(
                $this->getElement()->getName() . '[#{_id}][' . $columnName . '][]'
            )->setInputId(
                $this->getElement()->getId() . '_#{_id}_' . $columnName
            )->toHtml();
        }

        return parent::renderCellTemplate($columnName);
    }

    /**
     * Render the element HTML with JavaScript for dynamic filtering
     *
     * @param Varien_Data_Form_Element_Abstract $element
     * @return string
     */
    protected function _getElementHtml(Varien_Data_Form_Element_Abstract $element)
    {
        $html = parent::_getElementHtml($element);

        // Add JavaScript for dynamic status filtering and priority-based sorting
        $html .= $this->_getStatusMappingJavaScript();

        return $html;
    }

    /**
     * Get JavaScript for status mapping functionality
     *
     * @return string
     */
    protected function _getStatusMappingJavaScript()
    {
        return '
        <script type="text/javascript">
        //<![CDATA[

        // Update status options to hide already mapped statuses
        function updateStatusOptions() {
            var allSelects = document.querySelectorAll(".status-mapping-select");
            var mappedStatuses = [];

            // Collect all currently selected statuses
            allSelects.forEach(function(select) {
                var selectedOptions = select.querySelectorAll("option:checked");
                selectedOptions.forEach(function(option) {
                    if (option.value && mappedStatuses.indexOf(option.value) === -1) {
                        mappedStatuses.push(option.value);
                    }
                });
            });

            // Update each select to hide/show options
            allSelects.forEach(function(select) {
                var currentlySelected = [];
                var selectedOptions = select.querySelectorAll("option:checked");
                selectedOptions.forEach(function(option) {
                    currentlySelected.push(option.value);
                });

                var allOptions = select.querySelectorAll("option");
                allOptions.forEach(function(option) {
                    if (option.value) {
                        // Show option if it\'s currently selected in this dropdown or not mapped elsewhere
                        if (currentlySelected.indexOf(option.value) !== -1 || mappedStatuses.indexOf(option.value) === -1) {
                            option.style.display = "";
                            option.disabled = false;
                        } else {
                            option.style.display = "none";
                            option.disabled = true;
                        }
                    }
                });
            });
        }

        // Sort rows by priority when configuration is saved
        function sortMappingsByPriority() {
            var tbody = document.querySelector("#' . $this->getElement()->getId() . '_table tbody");
            if (!tbody) return;

            var rows = Array.from(tbody.querySelectorAll("tr"));

            rows.sort(function(a, b) {
                var priorityA = parseInt(a.querySelector("input[name*=\'[priority]\']")?.value || "999");
                var priorityB = parseInt(b.querySelector("input[name*=\'[priority]\']")?.value || "999");
                return priorityA - priorityB;
            });

            // Re-append rows in sorted order
            rows.forEach(function(row) {
                tbody.appendChild(row);
            });
        }

        // Initialize on page load
        document.observe("dom:loaded", function() {
            updateStatusOptions();

            // Add event listeners for new rows
            var addButton = document.querySelector("#' . $this->getElement()->getId() . '_add");
            if (addButton) {
                addButton.observe("click", function() {
                    setTimeout(function() {
                        updateStatusOptions();
                    }, 100);
                });
            }
        });

        // Update options when form is submitted to sort by priority
        if (typeof configForm !== "undefined") {
            configForm.observe("submit", function() {
                sortMappingsByPriority();
            });
        }

        //]]>
        </script>';
    }
}
