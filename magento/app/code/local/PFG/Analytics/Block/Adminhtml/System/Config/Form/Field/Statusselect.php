<?php
/**
 * PFG Analytics Status Select Renderer
 *
 * This block renders a multiselect dropdown for order statuses
 * in the system configuration.
 *
 * @package PFG_Analytics
 * <AUTHOR> Development Team
 */

class PFG_Analytics_Block_Adminhtml_System_Config_Form_Field_Statusselect extends Mage_Core_Block_Html_Select
{
    /**
     * Set input name
     *
     * @param string $value
     * @return PFG_Analytics_Block_Adminhtml_System_Config_Form_Field_Statusselect
     */
    public function setInputName($value)
    {
        // Ensure the name ends with [] for multiselect to work properly
        if (substr($value, -2) !== '[]') {
            $value .= '[]';
        }
        return $this->setName($value);
    }

    /**
     * Set input id
     *
     * @param string $value
     * @return PFG_Analytics_Block_Adminhtml_System_Config_Form_Field_Statusselect
     */
    public function setInputId($value)
    {
        return $this->setId($value);
    }

    /**
     * Render block HTML
     *
     * @return string
     */
    public function _toHtml()
    {
        if (!$this->getOptions()) {
            $this->setOptions($this->_getOrderStatuses());
        }
        
        // Set multiple attribute
        $this->setExtraParams('multiple="multiple" style="width:250px; height:80px;"');
        
        return parent::_toHtml();
    }

    /**
     * Get order statuses for options
     *
     * @return array
     */
    protected function _getOrderStatuses()
    {
        $statuses = Mage::helper('pfg_analytics')->getOrderStatuses();
        $options = array();
        
        foreach ($statuses as $code => $label) {
            $options[] = array(
                'value' => $code,
                'label' => $label
            );
        }
        
        return $options;
    }

    /**
     * Calculate option hash for selected values
     *
     * @param string|array $optionValue
     * @return string
     */
    public function calcOptionHash($optionValue)
    {
        if (is_array($optionValue)) {
            $optionValue = implode(',', $optionValue);
        }
        return sprintf('%u', crc32($this->getName() . $this->getId() . $optionValue));
    }
}
