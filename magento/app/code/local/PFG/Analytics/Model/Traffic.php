<?php
/**
 * PFG Analytics Traffic Model
 * 
 * This model handles traffic data collection and aggregation from various sources
 * including Magento native visitor tracking and external analytics services.
 * 
 * @package PFG_Analytics
 * <AUTHOR> Development Team
 */

class PFG_Analytics_Model_Traffic extends Mage_Core_Model_Abstract
{
    /**
     * Data source constants
     */
    const SOURCE_MAGENTO = 'magento';
    const SOURCE_CLOUDFLARE = 'cloudflare';
    const SOURCE_COMBINED = 'combined';

    /**
     * Initialize model
     */
    protected function _construct()
    {
        $this->_init('pfg_analytics/traffic');
    }

    /**
     * Get traffic data for specified date range
     *
     * @param string $fromDate Start date (Y-m-d format)
     * @param string $toDate End date (Y-m-d format)
     * @param int|null $storeId Store ID filter
     * @param string $source Data source filter
     * @return array Traffic data with totals and daily breakdown
     */
    public function getTrafficData($fromDate, $toDate, $storeId = null, $source = self::SOURCE_MAGENTO)
    {
        try {
            // Validate date range
            $helper = Mage::helper('pfg_analytics');
            $validation = $helper->validateDateRange($fromDate, $toDate);
            
            if (!$validation['valid']) {
                return array(
                    'error' => implode(' ', $validation['errors']),
                    'total_visits' => 0,
                    'total_unique_visitors' => 0,
                    'total_page_views' => 0,
                    'daily_data' => array()
                );
            }

            // Get data based on source
            switch ($source) {
                case self::SOURCE_MAGENTO:
                    return $this->_getMagentoTrafficData($fromDate, $toDate, $storeId);
                case self::SOURCE_CLOUDFLARE:
                    return $this->_getCloudflareTrafficData($fromDate, $toDate, $storeId);
                case self::SOURCE_COMBINED:
                    return $this->_getCombinedTrafficData($fromDate, $toDate, $storeId);
                default:
                    return $this->_getMagentoTrafficData($fromDate, $toDate, $storeId);
            }

        } catch (Exception $e) {
            Mage::logException($e);
            return array(
                'error' => 'Unable to load traffic data. Please try again.',
                'total_visits' => 0,
                'total_unique_visitors' => 0,
                'total_page_views' => 0,
                'daily_data' => array()
            );
        }
    }

    /**
     * Get Magento native traffic data
     *
     * @param string $fromDate Start date
     * @param string $toDate End date
     * @param int|null $storeId Store ID
     * @return array Traffic data
     */
    protected function _getMagentoTrafficData($fromDate, $toDate, $storeId = null)
    {
        $resource = Mage::getSingleton('core/resource');
        $readConnection = $resource->getConnection('core_read');
        
        // Get visitor data from log_visitor and log_visitor_info tables
        $visitorTable = $resource->getTableName('log/visitor');
        $visitorInfoTable = $resource->getTableName('log/visitor_info');
        
        // Build base query for unique visitors
        $visitorsSelect = $readConnection->select()
            ->from(array('v' => $visitorTable), array(
                'date' => 'DATE(v.first_visit_at)',
                'unique_visitors' => 'COUNT(DISTINCT v.visitor_id)',
                'total_visits' => 'COUNT(v.visitor_id)'
            ))
            ->where('DATE(v.first_visit_at) >= ?', $fromDate)
            ->where('DATE(v.first_visit_at) <= ?', $toDate);

        // Add store filter if specified
        if ($storeId !== null) {
            $visitorsSelect->where('v.store_id = ?', $storeId);
        }

        $visitorsSelect->group('DATE(v.first_visit_at)')
                      ->order('DATE(v.first_visit_at) ASC');

        $visitorsData = $readConnection->fetchAll($visitorsSelect);

        // Get page views from report_event table (if available)
        $pageViewsData = $this->_getPageViewsData($fromDate, $toDate, $storeId);

        // Combine data and calculate totals
        return $this->_combineTrafficData($visitorsData, $pageViewsData, $fromDate, $toDate);
    }

    /**
     * Get page views data from report_event table
     *
     * @param string $fromDate Start date
     * @param string $toDate End date
     * @param int|null $storeId Store ID
     * @return array Page views data
     */
    protected function _getPageViewsData($fromDate, $toDate, $storeId = null)
    {
        try {
            $resource = Mage::getSingleton('core/resource');
            $readConnection = $resource->getConnection('core_read');

            // Try to get the report_event table name, fallback if not available
            try {
                $eventTable = $resource->getTableName('report/event');
            } catch (Exception $e) {
                // Fallback to direct table name if report module config is not available
                $eventTable = $resource->getTableName('report_event');
            }

            // Check if report_event table exists
            if (!$readConnection->isTableExists($eventTable)) {
                return array();
            }

            $select = $readConnection->select()
                ->from($eventTable, array(
                    'date' => 'DATE(logged_at)',
                    'page_views' => 'COUNT(*)'
                ))
                ->where('DATE(logged_at) >= ?', $fromDate)
                ->where('DATE(logged_at) <= ?', $toDate)
                ->where('event_type_id = ?', 1); // Page view event type

            if ($storeId !== null) {
                $select->where('store_id = ?', $storeId);
            }

            $select->group('DATE(logged_at)')
                   ->order('DATE(logged_at) ASC');

            return $readConnection->fetchAll($select);

        } catch (Exception $e) {
            Mage::logException($e);
            return array();
        }
    }

    /**
     * Combine visitors and page views data
     *
     * @param array $visitorsData Visitors data
     * @param array $pageViewsData Page views data
     * @param string $fromDate Start date
     * @param string $toDate End date
     * @return array Combined traffic data
     */
    protected function _combineTrafficData($visitorsData, $pageViewsData, $fromDate, $toDate)
    {
        $dailyData = array();
        $totalVisits = 0;
        $totalUniqueVisitors = 0;
        $totalPageViews = 0;

        // Index page views data by date
        $pageViewsByDate = array();
        foreach ($pageViewsData as $row) {
            $pageViewsByDate[$row['date']] = (int)$row['page_views'];
        }

        // Process visitors data
        foreach ($visitorsData as $row) {
            $date = $row['date'];
            $visits = (int)$row['total_visits'];
            $uniqueVisitors = (int)$row['unique_visitors'];
            $pageViews = isset($pageViewsByDate[$date]) ? $pageViewsByDate[$date] : 0;

            $dailyData[] = array(
                'date' => $date,
                'visits' => $visits,
                'unique_visitors' => $uniqueVisitors,
                'page_views' => $pageViews
            );

            $totalVisits += $visits;
            $totalUniqueVisitors += $uniqueVisitors;
            $totalPageViews += $pageViews;
        }

        return array(
            'total_visits' => $totalVisits,
            'total_unique_visitors' => $totalUniqueVisitors,
            'total_page_views' => $totalPageViews,
            'daily_data' => $dailyData
        );
    }

    /**
     * Get Cloudflare traffic data (placeholder for future implementation)
     *
     * @param string $fromDate Start date
     * @param string $toDate End date
     * @param int|null $storeId Store ID
     * @return array Traffic data
     */
    protected function _getCloudflareTrafficData($fromDate, $toDate, $storeId = null)
    {
        // TODO: Implement Cloudflare API integration
        return array(
            'total_visits' => 0,
            'total_unique_visitors' => 0,
            'total_page_views' => 0,
            'daily_data' => array(),
            'note' => 'Cloudflare integration not yet implemented'
        );
    }

    /**
     * Get combined traffic data from multiple sources
     *
     * @param string $fromDate Start date
     * @param string $toDate End date
     * @param int|null $storeId Store ID
     * @return array Combined traffic data
     */
    protected function _getCombinedTrafficData($fromDate, $toDate, $storeId = null)
    {
        $magentoData = $this->_getMagentoTrafficData($fromDate, $toDate, $storeId);
        $cloudflareData = $this->_getCloudflareTrafficData($fromDate, $toDate, $storeId);

        // For now, just return Magento data
        // TODO: Implement proper data combination logic
        return $magentoData;
    }

    /**
     * Get chart data for specified period with grouping
     *
     * @param string $fromDate Start date
     * @param string $toDate End date
     * @param string $grouping Grouping option (day, week, month)
     * @param int|null $storeId Store ID
     * @param string $source Data source
     * @return array Chart data with labels and datasets
     */
    public function getChartDataForPeriod($fromDate, $toDate, $grouping = 'day', $storeId = null, $source = self::SOURCE_MAGENTO)
    {
        $trafficData = $this->getTrafficData($fromDate, $toDate, $storeId, $source);
        
        if (isset($trafficData['error'])) {
            return $trafficData;
        }

        // Group data according to specified grouping
        $groupedData = $this->_groupTrafficData($trafficData['daily_data'], $grouping);

        return array(
            'labels' => array_keys($groupedData),
            'visits' => array_values(array_map(function($item) { return $item['visits']; }, $groupedData)),
            'unique_visitors' => array_values(array_map(function($item) { return $item['unique_visitors']; }, $groupedData)),
            'page_views' => array_values(array_map(function($item) { return $item['page_views']; }, $groupedData))
        );
    }

    /**
     * Group traffic data by specified period
     *
     * @param array $dailyData Daily traffic data
     * @param string $grouping Grouping option
     * @return array Grouped data
     */
    protected function _groupTrafficData($dailyData, $grouping)
    {
        $grouped = array();

        foreach ($dailyData as $row) {
            $date = new DateTime($row['date']);
            
            switch ($grouping) {
                case 'week':
                    $key = $date->format('Y-W');
                    break;
                case 'month':
                    $key = $date->format('Y-m');
                    break;
                default: // day
                    $key = $date->format('Y-m-d');
                    break;
            }

            if (!isset($grouped[$key])) {
                $grouped[$key] = array(
                    'visits' => 0,
                    'unique_visitors' => 0,
                    'page_views' => 0
                );
            }

            $grouped[$key]['visits'] += $row['visits'];
            $grouped[$key]['unique_visitors'] += $row['unique_visitors'];
            $grouped[$key]['page_views'] += $row['page_views'];
        }

        return $grouped;
    }
}
