<?php
/**
 * PFG Analytics Product Attribute Source Model
 *
 * Provides dropdown options for product attributes that can be used for brand analytics
 *
 * @package PFG_Analytics
 * <AUTHOR> Development Team
 */

class PFG_Analytics_Model_System_Config_Source_Productattribute
{
    /**
     * Options array
     *
     * @var array
     */
    protected $_options;

    /**
     * Return options array
     *
     * @return array
     */
    public function toOptionArray()
    {
        if (!$this->_options) {
            $this->_options = array();
            
            // Add empty option
            $this->_options[] = array(
                'value' => '',
                'label' => '-- Please Select --'
            );
            
            try {
                // Get all product attributes that could be used for brands
                $attributes = Mage::getResourceModel('catalog/product_attribute_collection')
                    ->addVisibleFilter()
                    ->addFieldToFilter('frontend_input', array('in' => array('select', 'text', 'textarea')))
                    ->addFieldToFilter('attribute_code', array('neq' => 'status'))
                    ->addFieldToFilter('attribute_code', array('neq' => 'visibility'))
                    ->setOrder('frontend_label', 'ASC');

                foreach ($attributes as $attribute) {
                    $label = $attribute->getFrontendLabel();
                    $code = $attribute->getAttributeCode();
                    
                    // Skip attributes without proper labels
                    if (empty($label)) {
                        $label = $code;
                    }
                    
                    // Add attribute type info for clarity
                    $inputType = $attribute->getFrontendInput();
                    $typeLabel = '';
                    switch ($inputType) {
                        case 'select':
                            $typeLabel = ' (Dropdown)';
                            break;
                        case 'text':
                            $typeLabel = ' (Text)';
                            break;
                        case 'textarea':
                            $typeLabel = ' (Textarea)';
                            break;
                    }
                    
                    $this->_options[] = array(
                        'value' => $code,
                        'label' => $label . $typeLabel . ' [' . $code . ']'
                    );
                }
                
            } catch (Exception $e) {
                Mage::logException($e);
                // Add error option if something goes wrong
                $this->_options[] = array(
                    'value' => '',
                    'label' => 'Error loading attributes'
                );
            }
        }

        return $this->_options;
    }

    /**
     * Get options in "key-value" format
     *
     * @return array
     */
    public function toArray()
    {
        $array = array();
        foreach ($this->toOptionArray() as $option) {
            $array[$option['value']] = $option['label'];
        }
        return $array;
    }
}
