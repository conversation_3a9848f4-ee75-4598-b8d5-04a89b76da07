<?php
/**
 * PFG Analytics Product Display Format Source Model
 * 
 * This source model provides options for how products should be displayed
 * in the Top Selling Products analytics column.
 * 
 * @package PFG_Analytics
 * <AUTHOR> Development Team
 */
class PFG_Analytics_Model_System_Config_Source_Productdisplay
{
    /**
     * Options array for product display format
     * 
     * @return array
     */
    public function toOptionArray()
    {
        return array(
            array(
                'value' => 'name',
                'label' => Mage::helper('pfg_analytics')->__('Product Name')
            ),
            array(
                'value' => 'sku',
                'label' => Mage::helper('pfg_analytics')->__('Product SKU')
            ),
            array(
                'value' => 'name_sku',
                'label' => Mage::helper('pfg_analytics')->__('Name + SKU')
            )
        );
    }

    /**
     * Get options as array for use in forms
     * 
     * @return array
     */
    public function toArray()
    {
        $options = array();
        foreach ($this->toOptionArray() as $option) {
            $options[$option['value']] = $option['label'];
        }
        return $options;
    }
}
