<?xml version="1.0"?>
<!--
/**
 * PFG Analytics System Configuration
 * 
 * This file defines the system configuration structure for the PFG Analytics module.
 * Configuration can be found under System > Configuration > PFG > Analytics
 * 
 * @package PFG_Analytics
 * <AUTHOR> Development Team
 */
-->
<config>
    <tabs>
        <!-- PFG vendor tab -->
        <pfg translate="label" module="pfg_analytics">
            <label>PFG</label>
            <sort_order>400</sort_order>
        </pfg>
    </tabs>

    <sections>
        <!-- Analytics section under PFG tab -->
        <pfg_analytics translate="label" module="pfg_analytics">
            <label>Analytics</label>
            <tab>pfg</tab>
            <frontend_type>text</frontend_type>
            <sort_order>100</sort_order>
            <show_in_default>1</show_in_default>
            <show_in_website>1</show_in_website>
            <show_in_store>1</show_in_store>

            <groups>
                <!-- General settings group -->
                <general translate="label" module="pfg_analytics">
                    <label>General Settings</label>
                    <frontend_type>text</frontend_type>
                    <sort_order>10</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>

                    <fields>
                        <!-- Module enable/disable -->
                        <enabled translate="label comment" module="pfg_analytics">
                            <label>Enable Analytics Module</label>
                            <comment>Enable or disable the PFG Analytics module</comment>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>10</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </enabled>
                    </fields>
                </general>

                <!-- Status mapping group -->
                <status_mapping translate="label" module="pfg_analytics">
                    <label>Status Mapping Configuration</label>
                    <frontend_type>text</frontend_type>
                    <sort_order>20</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>

                    <fields>
                        <!-- Status mappings configuration -->
                        <mappings translate="label comment" module="pfg_analytics">
                            <label>Custom Status Mappings</label>
                            <comment><![CDATA[Configure custom status names and map them to real order statuses. Each custom status can group multiple real order statuses together. Use priority to control display order (lower numbers appear first).]]></comment>
                            <frontend_model>pfg_analytics/adminhtml_system_config_form_field_statusmapping</frontend_model>
                            <backend_model>pfg_analytics/system_config_backend_statusmapping</backend_model>
                            <sort_order>10</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </mappings>

                        <!-- Configuration helper text -->
                        <mapping_help translate="label comment" module="pfg_analytics">
                            <label>Configuration Help</label>
                            <comment><![CDATA[
                                <strong>How to configure status mappings:</strong><br/>
                                1. Click "Add Status Mapping" to create a new mapping<br/>
                                2. Set a priority number (lower numbers appear first in analytics)<br/>
                                3. Enter a custom status name (e.g., "Completed Orders")<br/>
                                4. Select one or more real order statuses to group under this custom name<br/>
                                5. Save the configuration<br/><br/>
                                <strong>Example:</strong> Create a custom status called "Completed Orders" with priority 10 and map it to "complete", "shipped", and "delivered" real statuses. In the analytics, you'll see "Completed Orders" with a breakdown showing the individual counts.
                            ]]></comment>
                            <frontend_type>label</frontend_type>
                            <sort_order>20</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </mapping_help>
                    </fields>
                </status_mapping>

                <!-- Top Selling Configuration group -->
                <top_selling translate="label" module="pfg_analytics">
                    <label>Top Selling Configuration</label>
                    <frontend_type>text</frontend_type>
                    <sort_order>30</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>

                    <fields>
                        <!-- Ignore categories configuration -->
                        <ignore_categories translate="label comment" module="pfg_analytics">
                            <label>Ignore Categories for Top Selling Analytics</label>
                            <comment><![CDATA[Enter category IDs to exclude from all Top Selling analytics, separated by commas (e.g., 3,76,77). Leave empty to include all categories.]]></comment>
                            <frontend_type>text</frontend_type>
                            <sort_order>10</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </ignore_categories>

                        <!-- Brand attribute configuration -->
                        <brand_attribute_code translate="label comment" module="pfg_analytics">
                            <label>Brand Attribute for Top Selling Brands</label>
                            <comment><![CDATA[Select the product attribute used for brands. Leave empty to disable brand analytics.]]></comment>
                            <frontend_type>select</frontend_type>
                            <source_model>pfg_analytics/system_config_source_productattribute</source_model>
                            <sort_order>30</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </brand_attribute_code>

                        <!-- Product display format configuration -->
                        <product_display_format translate="label comment" module="pfg_analytics">
                            <label>Product Display Format</label>
                            <comment><![CDATA[Choose how to display products in the Top Selling Products analytics.]]></comment>
                            <frontend_type>select</frontend_type>
                            <source_model>pfg_analytics/system_config_source_productdisplay</source_model>
                            <sort_order>40</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </product_display_format>

                        <!-- Configuration helper text -->
                        <configuration_help translate="label comment" module="pfg_analytics">
                            <label>Configuration Help</label>
                            <comment><![CDATA[
                                <strong>Top Selling Analytics Configuration:</strong><br/><br/>
                                <strong>Category Exclusions:</strong><br/>
                                • Enter category IDs separated by commas (e.g., 3,76,77)<br/>
                                • Affects all three analytics columns (Categories, Brands, Products)<br/>
                                • Leave empty to include all categories<br/><br/>
                                <strong>Brand Analytics:</strong><br/>
                                • Enter the product attribute code used for brands<br/>
                                • Common codes: 'manufacturer', 'brand', 'brand_name'<br/>
                                • Leave empty to disable brand analytics column<br/><br/>
                                <strong>Product Display:</strong><br/>
                                • Choose how products appear in the analytics<br/>
                                • Options: Name only, SKU only, or Name + SKU combined
                            ]]></comment>
                            <frontend_type>label</frontend_type>
                            <sort_order>50</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </configuration_help>
                    </fields>
                </top_selling>
            </groups>
        </pfg_analytics>
    </sections>
</config>
