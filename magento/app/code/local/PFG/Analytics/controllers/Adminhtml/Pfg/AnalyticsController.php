<?php
/**
 * PFG Analytics Admin Controller
 * 
 * This controller handles admin requests for the PFG Analytics module.
 * It provides the main analytics interface and configuration functionality.
 * 
 * @package PFG_Analytics
 * <AUTHOR> Development Team
 */

class PFG_Analytics_Adminhtml_Pfg_AnalyticsController extends Mage_Adminhtml_Controller_Action
{
    /**
     * Index Action - Main analytics page
     * 
     * This action displays the analytics dashboard with sales data,
     * charts, and configuration options.
     * 
     * @return void
     */
    public function indexAction()
    {
        try {
            // Check if module is enabled
            $helper = Mage::helper('pfg_analytics');
            if (!$helper->isEnabled()) {
                $this->_getSession()->addError($this->__('PFG Analytics module is disabled. Please enable it in System > Configuration > PFG > Analytics.'));
                $this->_redirect('adminhtml/dashboard');
                return;
            }

            // Validate form key for POST requests
            if ($this->getRequest()->isPost() && !$this->_validateFormKey()) {
                $this->_getSession()->addError($this->__('Invalid form key. Please reload the page.'));
                $this->_redirect('*/*/');
                return;
            }

            // Validate date parameters if provided
            $fromDate = $this->getRequest()->getParam('from_date');
            $toDate = $this->getRequest()->getParam('to_date');
            $helper = Mage::helper('pfg_analytics');

            if ($fromDate && !$helper->validateDate($fromDate)) {
                $this->_getSession()->addError($this->__('Invalid from date format. Please use YYYY-MM-DD format.'));
                $this->_redirect('*/*/');
                return;
            }

            if ($toDate && !$helper->validateDate($toDate)) {
                $this->_getSession()->addError($this->__('Invalid to date format. Please use YYYY-MM-DD format.'));
                $this->_redirect('*/*/');
                return;
            }

            // Validate date range
            if ($fromDate && $toDate) {
                $fromDateTime = new DateTime($fromDate);
                $toDateTime = new DateTime($toDate);

                if ($fromDateTime > $toDateTime) {
                    $this->_getSession()->addError($this->__('From date cannot be later than to date.'));
                    $this->_redirect('*/*/');
                    return;
                }

                // Check if date range is too large (more than 2 years)
                $interval = $fromDateTime->diff($toDateTime);
                if ($interval->days > 730) {
                    $this->_getSession()->addError($this->__('Date range cannot exceed 2 years.'));
                    $this->_redirect('*/*/');
                    return;
                }
            }

            // Validate status parameters
            $selectedStatuses = $this->getRequest()->getParam('selected_statuses');
            if ($selectedStatuses && is_array($selectedStatuses)) {
                $availableStatuses = array_keys($helper->getOrderStatuses());
                foreach ($selectedStatuses as $status) {
                    if (!in_array($status, $availableStatuses)) {
                        $this->_getSession()->addError($this->__('Invalid order status selected.'));
                        $this->_redirect('*/*/');
                        return;
                    }
                }
            }

            // Set the page title
            $this->_title($this->__('PFG'))->_title($this->__('Analytics'));

            // Load the admin layout
            $this->loadLayout();

            // Set the active menu item
            $this->_setActiveMenu('pfg/analytics');

            // Add breadcrumb navigation
            $this->_addBreadcrumb(
                Mage::helper('pfg_analytics')->__('PFG'),
                Mage::helper('pfg_analytics')->__('PFG')
            );
            $this->_addBreadcrumb(
                Mage::helper('pfg_analytics')->__('Analytics'),
                Mage::helper('pfg_analytics')->__('Analytics')
            );

            // Create and add the main content block
            $block = $this->getLayout()->createBlock('pfg_analytics/adminhtml_analytics');
            $this->_addContent($block);

            // Render the complete layout
            $this->renderLayout();

        } catch (Exception $e) {
            Mage::logException($e);
            $this->_getSession()->addError($this->__('An error occurred while loading the analytics page. Please try again.'));
            $this->_redirect('adminhtml/dashboard');
        }
    }



    /**
     * Clear analytics cache action
     */
    public function clearCacheAction()
    {
        try {
            // Validate form key
            if (!$this->_validateFormKey()) {
                $this->_getSession()->addError($this->__('Invalid form key. Please reload the page.'));
                $this->_redirect('*/*/');
                return;
            }

            // Clear analytics cache
            $cache = Mage::app()->getCache();
            $cache->clean(array(PFG_Analytics_Helper_Data::CACHE_TAG));

            $this->_getSession()->addSuccess($this->__('Analytics cache has been cleared successfully.'));

        } catch (Exception $e) {
            Mage::logException($e);
            $this->_getSession()->addError($this->__('An error occurred while clearing the cache. Please try again.'));
        }

        $this->_redirect('*/*/');
    }

    /**
     * Check if user has permission to access this controller
     *
     * @return bool True if user has access, false otherwise
     */
    protected function _isAllowed()
    {
        return Mage::getSingleton('admin/session')->isAllowed('admin/pfg/analytics');
    }
}
