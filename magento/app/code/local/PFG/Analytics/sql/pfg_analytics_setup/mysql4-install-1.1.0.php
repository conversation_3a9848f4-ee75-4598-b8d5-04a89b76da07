<?php
/**
 * PFG Analytics Setup Script - Traffic Analytics Table
 * 
 * This script creates the traffic analytics summary table for storing
 * aggregated traffic data from various sources.
 * 
 * @package PFG_Analytics
 * <AUTHOR> Development Team
 */

$installer = $this;
$installer->startSetup();

try {
    // Create traffic summary table
    $table = $installer->getConnection()
        ->newTable($installer->getTable('pfg_analytics/traffic_summary'))
        ->addColumn('id', Varien_Db_Ddl_Table::TYPE_INTEGER, null, array(
            'identity'  => true,
            'unsigned'  => true,
            'nullable'  => false,
            'primary'   => true,
        ), 'Traffic Summary ID')
        ->addColumn('date', Varien_Db_Ddl_Table::TYPE_DATE, null, array(
            'nullable'  => false,
        ), 'Date')
        ->addColumn('store_id', Varien_Db_Ddl_Table::TYPE_SMALLINT, null, array(
            'unsigned'  => true,
            'nullable'  => true,
        ), 'Store ID')
        ->addColumn('visits', Varien_Db_Ddl_Table::TYPE_INTEGER, null, array(
            'unsigned'  => true,
            'nullable'  => false,
            'default'   => '0',
        ), 'Total Visits')
        ->addColumn('unique_visitors', Varien_Db_Ddl_Table::TYPE_INTEGER, null, array(
            'unsigned'  => true,
            'nullable'  => false,
            'default'   => '0',
        ), 'Unique Visitors')
        ->addColumn('page_views', Varien_Db_Ddl_Table::TYPE_INTEGER, null, array(
            'unsigned'  => true,
            'nullable'  => false,
            'default'   => '0',
        ), 'Page Views')
        ->addColumn('source', Varien_Db_Ddl_Table::TYPE_VARCHAR, 20, array(
            'nullable'  => false,
            'default'   => 'magento',
        ), 'Data Source')
        ->addColumn('created_at', Varien_Db_Ddl_Table::TYPE_TIMESTAMP, null, array(
            'nullable'  => false,
            'default'   => Varien_Db_Ddl_Table::TIMESTAMP_INIT,
        ), 'Created At')
        ->addColumn('updated_at', Varien_Db_Ddl_Table::TYPE_TIMESTAMP, null, array(
            'nullable'  => false,
            'default'   => Varien_Db_Ddl_Table::TIMESTAMP_INIT_UPDATE,
        ), 'Updated At')
        ->addIndex(
            $installer->getIdxName('pfg_analytics/traffic_summary', array('date', 'store_id')),
            array('date', 'store_id')
        )
        ->addIndex(
            $installer->getIdxName('pfg_analytics/traffic_summary', array('source')),
            array('source')
        )
        ->addIndex(
            $installer->getIdxName('pfg_analytics/traffic_summary', array('date')),
            array('date')
        )
        ->addForeignKey(
            $installer->getFkName('pfg_analytics/traffic_summary', 'store_id', 'core/store', 'store_id'),
            'store_id',
            $installer->getTable('core/store'),
            'store_id',
            Varien_Db_Ddl_Table::ACTION_SET_NULL,
            Varien_Db_Ddl_Table::ACTION_CASCADE
        )
        ->setComment('PFG Analytics Traffic Summary Table');

    $installer->getConnection()->createTable($table);

    // Log successful installation
    Mage::log('PFG Analytics: Traffic summary table created successfully', Zend_Log::INFO);

} catch (Exception $e) {
    // Log error and re-throw
    Mage::logException($e);
    throw $e;
}

$installer->endSetup();
