<?php
/**
 * Product list template
 *
 * @see Mage_Catalog_Block_Product_List
 */
?>
<?php
    $_productCollection=$this->getLoadedProductCollection();
    $_helper = $this->helper('catalog/output');

    // Initialize batch OnSale label processing for better performance
    $_onsaleBatchBlock = null;
    $_useBatchProcessing = Mage::getStoreConfigFlag('onsale/performance/enable_batch_processing');
    if ($_useBatchProcessing) {
        $_onsaleBatchBlock = Mage::app()->getLayout()->createBlock('onsale/catalog_product_list_batch');
        $_onsaleBatchBlock->setProductCollection($_productCollection);
    }
?>

<?php if(!$_productCollection->count()): ?>
    <p class="note-msg"><?php echo $this->__('There are no products in this category') ?></p>
<?php else: ?>
    <?php $_collectionSize = $_productCollection->count() ?>
    <?php $_columnCount = $this->getColumnCount(); ?>
    <?php $counter = 0; ?>

    <div class="filtersContentWrapper">
        <div class="filterContent">
            <a href="javascript:;" class="responsiveOpenFilters"><?php echo $this->__('Filter') ?></a>
            <div class="filtersWrapper">
                <?php echo $this->getChildHtml('catalog.leftnav'); ?>
            </div>
            <?php $catalogLeftNavBlock = $this->getChild('catalog.leftnav') ?>
            <?php if ($catalogLeftNavBlock): ?>
                <?php echo $catalogLeftNavBlock->getStateHtml(); ?>
            <?php endif ?>
        </div>
    </div>

    <div class="toolbarBox">
        <?php echo $this->getToolbarHtml() ?>
    </div>

    <div class="productListing category-products">
        <?php if($this->getMode()!='grid'): ?>
        <?php else: ?>
            <?php foreach ($_productCollection as $_product): ?>
                <div class="productBox">
                    <a href="<?php echo $_product->getProductUrl() ?>" class="productIMGLink" rel="nofollow">
                        <div class="onsale-category-container-grid">
                            <?php echo $_useBatchProcessing && $_onsaleBatchBlock ? $_onsaleBatchBlock->getProductLabelHtml($_product) : Mage::helper('onsale')->getCategoryLabelHtml($_product); ?>
                            <img class="first" src="<?php echo $this->helper('catalog/image')->init($_product, 'small_image')->resize(280, 280); ?>" alt="<?php echo $this->stripTags($this->getImageLabel($_product, 'small_image'), null, true) ?>" title="<?php echo $this->stripTags($this->getImageLabel($_product, 'small_image'), null, true) ?>" />
                            <?php echo Mage::app()->getLayout()->getBlock('product.labels')->setProduct($_product)->toHtml(); ?>
                        </div>
                    </a>
                    <a href="<?php echo $_product->getProductUrl() ?>" class="productTitle"><?php echo $_helper->productAttribute($_product, $_product->getName(), 'name') ?></a>
                    <?php echo $this->getPriceHtml($_product, true) ?>
                </div>
            <?php endforeach ?>
        <?php endif; ?>
    </div>

    <?php $pagerHtml = $this->getChild('toolbar')->getPagerHtml(); ?>
    <?php if (trim($pagerHtml)): ?>
        <div class="pagingBox">
            <?php echo $pagerHtml; ?>
        </div>
    <?php endif ?>

    <script>
         jQuery(function($){

             if ($(window).width() < 1024) {

                $('.responsiveOpenFilters').click(function() {

                    $(this).next().slideToggle();
                    $(this).parent().toggleClass('filtersAreOpened');

                });

             }

         });
     </script>

<?php endif; ?>