<?xml version="1.0"?>
<layout>
    
    <default>
        <reference name="top.links">
            <remove name="checkout_cart_link"/>
            <block type="checkout/links" name="checkout_cart_link_custom"><action method="addCartLink" ></action></block>
            <block type="opc/links" name="checkout_cart_link_custom"><action method="addCheckoutLink"></action></block>
        </reference>

        <reference name="head">
            <action method="addItem" ifconfig="opc/paypallogin/status"><type>skin_js</type><name>js/iwd/opc/login.js</name></action>            
            <action method="addItem" ifconfig="opc/paypallogin/status"><type>skin_css</type><name>css/iwd/opc/paypal.css</name><params/></action>
            <action method="addItem" ifconfig="payment/incontext/enable"><type>skin_js</type><name>js/iwd/opc/paypal-in-context.js</name></action>
        </reference>

        <reference name="before_body_end">
            <block type="opc/lipp" name="opc.lipp.js" as="">
                <action method="setTemplate"><template>opc/lipp-js.phtml</template></action>
            </block>
        </reference>
    </default>
    
    <opc_index_index translate="label">
        <label>Onepage Checkout</label>
                
        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>
        
        <reference name="head">     
            <action method="addItem"><type>skin_js</type><name>js/iwd/opc/checkout.js</name></action>
            <action method="addItem"><type>skin_js</type><name>js/iwd/opc/extend.js</name></action>
            <action method="addJs"><file>mage/directpost.js</file></action>
            <action method="addJs"><script>mage/centinel.js</script></action>
            <action method="addItem"><type>skin_js</type><name>js/opcheckout.js</name></action>
            <action method="addCss"><stylesheet>css/iwd/opc/opc.css</stylesheet></action>
            <action method="addJs" ifconfig="payment/braintree/active"><file>braintree/braintree-1.3.4.js</file></action>
            <action method="addCss" ifconfig="payment/braintree/active"><stylesheet>braintree/css/braintree.css</stylesheet></action>
        </reference>

        <reference name="breadcrumbs">
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title">
                <crumbName>home</crumbName>
                <crumbInfo><label>Home</label><title>Go to Home Page</title><link>/</link></crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="customer">
                <crumbName>Checkout</crumbName>
                <crumbInfo>
                    <label>Checkout</label>
                    <title>Checkout</title>
                </crumbInfo>
            </action>
        </reference>
        
        <reference name="content">          
            <block type="opc/wrapper" name="es.checkout.container" template="opc/wrapper.phtml">
            
                <block type="page/html_wrapper" name="paypal.button" translate="label">
                    <label>PayPal Express Checkout Shortcut Wrapper</label>
                    <block type="paypal/express_shortcut" name="product.info.addtocart.paypal" template="opc/paypal/express/shortcut.phtml">
                        <!-- action method="setIsInCatalogProduct"><value>1</value></action -->
                    </block>
                </block>        
        
                <!--  LOGIN FORM  -->
                <block type="opc/onepage_login" name="checkout.onepage.login" as="login" template="opc/onepage/login.phtml">
                    <block type="customer/account_forgotpassword" name="forgotPassword" template="opc/customer/form/forgotpassword.phtml"/> 
                </block>
                <!-- BILLING FORM -->
                <block type="opc/onepage_billing" name="checkout.onepage.billing" as="billing" template="opc/onepage/billing.phtml"/>

                <!-- SHIPPING FORM -->
                <block type="opc/onepage_shipping" name="checkout.onepage.shipping" as="shipping" template="opc/onepage/shipping.phtml"/>
                
                <!-- COUPON FORM -->
                <block type="checkout/cart_coupon" name="checkout.cart.coupon" as="coupon" template="opc/onepage/coupon.phtml"/>
                
                <!-- COMMENT FORM -->
                <block type="opc/onepage_comment" name="checkout.order.comment" as="customer.comment"/>
                
                <!-- SHIPPING METHODS FORM -->
                <block type="checkout/onepage_shipping_method" name="checkout.onepage.shipping_method" as="shipping_method" template="opc/onepage/shipping_method.phtml">
                    <block type="checkout/onepage_shipping_method_available" name="checkout.onepage.shipping_method.available" as="available" template="checkout/onepage/shipping_method/available.phtml"/>
                    <block type="checkout/onepage_shipping_method_additional" name="checkout.onepage.shipping_method.additional" as="additional" template="checkout/onepage/shipping_method/additional.phtml"/>
                </block>
                
                <!-- PAYMENTS METHOD FORM -->
                <block type="checkout/onepage_payment" name="checkout.onepage.payment" as="payment" template="opc/onepage/payment.phtml">
                    <block type="checkout/onepage_payment_methods" name="checkout.payment.methods" as="methods" template="checkout/onepage/payment/methods.phtml">
                        <action method="setMethodFormTemplate"><method>purchaseorder</method><template>payment/form/purchaseorder.phtml</template></action>
                    </block>
                </block>
                
                <block type="checkout/agreements" name="checkout.onepage.agreements" as="agreements" template="opc/onepage/agreements.phtml">
                </block>
                <block type="opc/onepage_subscribed" template="opc/onepage/review/subscribed.phtml" name="opc.newsletters" />

            </block>
        </reference>
        
        <!--  PAYPAL EXPRESS LIGHTBOX -->
        <reference name="before_body_end">
            <!--<block type="opc/lipp" name="opc.lipp.js" as="">-->
                <!--<action method="setTemplate" ifconfig="opc/paypal/status"><template>opc/lipp-js.phtml</template></action>-->
            <!--</block>-->
            <block type="opc/braintree_datajs" name="braintree_payments_data_js">
                <action method="setTemplate" ifconfig="payment/braintree/active"><template>braintree/data_js.phtml</template></action>
            </block>
            <block type="opc/braintree_form" name="payment.form.braintree">
                <action method="setTemplate" ifconfig="payment/braintree/active"><template>braintree/review_js.phtml</template></action>
                <action method="setMethodInfo"></action>
            </block>
        </reference>
    </opc_index_index>      
    
    <checkout_onepage_review>
        <reference name="checkout.onepage.review.button">
            <action method="setTemplate" ifconfig="opc/global/status"><template>opc/onepage/review/button.phtml</template></action>
        </reference>
        <reference name="checkout.onepage.agreements">
            <action method="setTemplate" ifconfig="opc/global/status"><template>opc/onepage/agreements.phtml</template></action>
        </reference>        
    </checkout_onepage_review>
    
    <opc_json_savepayment>
        <reference name="checkout.onepage.review.info.items.before">
            <block type="centinel/authentication" name="centinel.frame" template="centinel/authentication.phtml">
                <action method="addRelatedBlock"><blockId>checkout-review-submit</blockId></action>
                <action method="addRelatedBlock"><blockId>checkout-review-table-wrapper</blockId></action>
                <action method="setAuthenticationStartMode"><mode>instant</mode></action>
            </block>
        </reference>
    </opc_json_savepayment>
    
    <centinel_index_authenticationcomplete>
        <reference name="root">
            <action method="setTemplate"><template>opc/centinel/authentication/complete.phtml</template></action>
        </reference>
    </centinel_index_authenticationcomplete>
    
    <customer_logged_out>
        <reference name="top.links">
            <action method="addLink" translate="label title" module="opc" ifconfig="opc/paypallogin/status"><label></label><url helper="opc/paypal/getLoginUrl"/><title>Log In With Paypal</title><prepare/><urlParams/><position>100</position><liParams/><aParams><id>topPayPalIn</id><class>paypal-auth</class></aParams></action>
        </reference>
    </customer_logged_out>
    
    <customer_account_index>
        <reference name="customer_account_dashboard_info">
            <block type="opc/customer_account_dashboard_info" name="customer_account_dashboard_info_paypal" as="paypalauth_dashboard" template="opc/customer/account/dashboard/info.phtml"/>
        </reference>
    </customer_account_index>

   <opc_paypal_asklink>
        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>
         <reference name="content">
             <block type="opc/customer_account_asklink" name="opc.paypal.auth" template="opc/customer/account/asklink.phtml"/>
         </reference>
    </opc_paypal_asklink>

</layout>
