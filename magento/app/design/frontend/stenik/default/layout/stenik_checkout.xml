<?xml version="1.0"?>
<layout>
    <checkout_onepage_index_customer_must_be_logged>
        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>

        <reference name="breadcrumbs">
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title">
                <crumbName>home</crumbName>
                <crumbInfo><label>Home</label><title>Go to Home Page</title><link>/</link></crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="customer">
                <crumbName>Checkout</crumbName>
                <crumbInfo>
                    <label>Checkout</label>
                    <title>Checkout</title>
                </crumbInfo>
            </action>
        </reference>

        <reference name="content">
            <block type="core/template" name="stenik_checkout.must_login_checkout" template="stenik/checkout/checkout/must-login-page.phtml">
                <block type="checkout/onepage_login" name="checkout.onepage.login" as="login" template="stenik/checkout/checkout/onepage/login.phtml">
                    <block type="inchoo_socialconnect/checkout" name="inchoo_socialconnect_checkout" as="inchooSocialconnect" template="stenik/checkout/checkout/onepage/login/inchoo-socialconnect.phtml">
                        <block type="inchoo_socialconnect/facebook_button" name="inchoo_socialconnect_checkout_facebook_button" />
                        <block type="inchoo_socialconnect/google_button" name="inchoo_socialconnect_checkout_google_button" />
                    </block>
                </block>
                <block type="stenik_checkout/onepage_section" name="checkout.onepage.section.review" template="stenik/checkout/checkout/onepage/section.phtml">
                    <action method="setSectionGroup"><section>review</section></action>
                    <action method="setSectionCode"><section>review</section></action>
                    <action method="setSectionHtmlClass"><class>review</class></action>
                    <block type="checkout/onepage_review" name="checkout.onepage.review" template="stenik/checkout/checkout/onepage/review.phtml">
                        <block type="checkout/onepage_review_info" name="checkout.onepage.review.info" template="stenik/checkout/checkout/onepage/review/info.phtml">
                            <action method="addItemRender"><type>default</type><block>checkout/cart_item_renderer</block><template>stenik/checkout/checkout/onepage/review/item.phtml</template></action>
                            <action method="addItemRender"><type>grouped</type><block>checkout/cart_item_renderer_grouped</block><template>stenik/checkout/checkout/onepage/review/item.phtml</template></action>
                            <action method="addItemRender"><type>configurable</type><block>checkout/cart_item_renderer_configurable</block><template>stenik/checkout/checkout/onepage/review/item.phtml</template></action>
                            <block type="checkout/cart_totals" name="checkout.onepage.review.info.totals" as="totals" template="checkout/onepage/review/totals.phtml"/>
                            <block type="core/text_list" name="checkout.onepage.review.info.items.before" as="items_before" translate="label">
                                <label>Items Before</label>
                            </block>
                            <block type="core/text_list" name="checkout.onepage.review.info.items.after" as="items_after" translate="label">
                                <label>Items After</label>
                            </block>
                        </block>
                    </block>
                </block>
            </block>
        </reference>

        <remove name="right" />
    </checkout_onepage_index_customer_must_be_logged>

    <checkout_onepage_index>
        <reference name="head">
            <action method="addItem"><type>skin_js</type><script>js/select2/select2.full.min.js</script><param>data-jquery</param></action>
            <action method="addItem"><type>skin_js</type><script>js/select2/i18n/bg.js</script><param>data-jquery</param></action>
            <action method="addItem"><type>skin_css</type><name>css/select2.min.css</name></action>
            <action method="addItem"><type>skin_js</type><name>js/stenik.tabs.js</name><group>data-jquery</group></action>
        </reference>

        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>

        <remove name="right" />
        <remove name="checkout.onepage"></remove>

        <reference name="content">
            <block type="stenik_checkout/onepage" name="checkout.onepage.mergedsteps" template="stenik/checkout/checkout/onepage.phtml">

                <block type="checkout/onepage_login" name="checkout.onepage.login" as="login_popup" template="stenik/checkout/checkout/onepage/login-popup.phtml">
                    <block type="inchoo_socialconnect/checkout" name="inchoo_socialconnect_checkout" as="inchooSocialconnect" template="stenik/checkout/checkout/onepage/login/inchoo-socialconnect.phtml">
                        <block type="inchoo_socialconnect/facebook_button" name="inchoo_socialconnect_checkout_facebook_button" />
                        <block type="inchoo_socialconnect/google_button" name="inchoo_socialconnect_checkout_google_button" />
                    </block>
                </block>

                <block type="stenik_checkout/onepage_step" name="checkout.onepage.step.onestep" as="onestep" template="stenik/checkout/checkout/onepage/step/default.phtml">
                    <action method="setIsLastStep"><val>1</val></action>
                    <block type="stenik_checkout/onepage_section" name="checkout.onepage.section.billing" template="stenik/checkout/checkout/onepage/section.phtml">
                        <action method="setSectionGroup"><section>billing</section></action>
                        <action method="setSectionCode"><section>billing</section></action>
                        <action method="setSectionHtmlClass"><class>billingInfo</class></action>
                        <action method="setUpdateSectionGroupsOnChange">
                            <sections_groups>
                                <shipping_method/>
                                <payment_method/>
                                <coupon/>
                                <review/>
                                <comment_agreements/>
                            </sections_groups>
                        </action>
                        <block type="checkout/onepage_billing" name="checkout.onepage.billing" as="billing" template="stenik/checkout/checkout/onepage/billing.phtml">
                            <block type="core/text_list" name="checkout.onepage.billing.additional_address_fields" as="additional_address_fields"/>
                        </block>
                        <block type="core/template" name="checkout.onepage.billing.scripts" as="scripts" template="stenik/checkout/checkout/onepage/billing/scripts.phtml"/>
                    </block>
                    <block type="stenik_checkout/onepage_section" name="checkout.onepage.section.shipping_method" template="stenik/checkout/checkout/onepage/section.phtml">
                        <action method="setSectionGroup"><section>shipping_method</section></action>
                        <action method="setSectionCode"><section>shipping_method</section></action>
                        <action method="setSectionHtmlClass"><class>methods</class></action>
                        <action method="setUpdateSectionGroupsOnChange">
                            <sections_groups>
                                <payment_method/>
                                <coupon/>
                                <review/>
                                <comment_agreements/>
                            </sections_groups>
                        </action>
                        <block type="checkout/onepage_shipping_method" name="checkout.onepage.shipping_method" as="shipping_method" template="stenik/checkout/checkout/onepage/shipping_method.phtml">
                            <block type="stenik_checkout/onepage_shipping_method_available" name="checkout.onepage.shipping_method.available" as="available" template="stenik/checkout/checkout/onepage/shipping_method/available.phtml"/>
                            <block type="checkout/onepage_shipping_method_additional" name="checkout.onepage.shipping_method.additional" as="additional" template="checkout/onepage/shipping_method/additional.phtml"/>
                        </block>
                    </block>
                    <block type="stenik_checkout/onepage_section" name="checkout.onepage.section.payment_method" template="stenik/checkout/checkout/onepage/section.phtml">
                        <action method="setSectionGroup"><section>payment_method</section></action>
                        <action method="setSectionCode"><section>payment_method</section></action>
                        <action method="setSectionHtmlClass"><class>methods</class></action>
                        <action method="setUpdateSectionGroupsOnChange">
                            <sections_groups>
                                <coupon/>
                                <review/>
                                <comment_agreements/>
                            </sections_groups>
                        </action>
                        <block type="checkout/onepage_payment_methods" name="checkout.payment.methods" as="methods" template="stenik/checkout/checkout/onepage/payment/methods.phtml">
                            <action method="setMethodFormTemplate"><method>purchaseorder</method><template>payment/form/purchaseorder.phtml</template></action>
                            <block type="core/template" name="checkout.onepage.payment.methods.scripts" as="scripts" />
                            <block type="core/template" name="checkout.onepage.payment.methods.additional" as="additional" />
                        </block>
                    </block>
                    <block type="stenik_checkout/onepage_section" name="checkout.onepage.section.comment.agreements" template="stenik/checkout/checkout/onepage/section.phtml">
                        <action method="setSectionGroup"><section>comment_agreements</section></action>
                        <action method="setSectionCode"><section>comment_agreements</section></action>
                        <action method="setSectionHtmlClass"><class>comment_agreements</class></action>
                        <action method="setSectionPreserveValuesOnReload"><flag>1</flag></action>
                        <block type="core/template" name="checkout.onepage.comment.agreements.wrapper" template="stenik/checkout/checkout/onepage/comment-agreements.phtml">
                            <block type="core/template" name="checkout.onepage.comment" as="comment" template="stenik/checkout/checkout/onepage/comment-agreements/comment.phtml"/>
                            <block type="checkout/agreements" name="checkout.onepage.agreements" as="agreements" template="stenik/checkout/checkout/onepage/comment-agreements/agreements.phtml"/>
                        </block>
                    </block>
                    <block type="stenik_checkout/onepage_section" name="checkout.onepage.section.coupon" template="stenik/checkout/checkout/onepage/section.phtml">
                        <action method="setSectionGroup"><section>coupon</section></action>
                        <action method="setSectionCode"><section>coupon</section></action>
                        <action method="setSectionHtmlClass"><class>coupon</class></action>
                        <action method="setSectionDisableAutosubmit"><flag>1</flag></action>
                        <action method="setUpdateSectionGroupsOnChange">
                            <sections_groups>
                                <coupon/>
                                <review/>
                                <comment_agreements/>
                            </sections_groups>
                        </action>
                        <block type="checkout/cart_coupon" name="checkout.onepage.coupon" template="stenik/checkout/checkout/onepage/coupon.phtml"/>
                    </block>
                    <block type="stenik_checkout/onepage_section" name="checkout.onepage.section.review" template="stenik/checkout/checkout/onepage/section.phtml">
                        <action method="setSectionGroup"><section>review</section></action>
                        <action method="setSectionCode"><section>review</section></action>
                        <action method="setSectionHtmlClass"><class>review</class></action>
                        <block type="checkout/onepage_review" name="checkout.onepage.review" template="stenik/checkout/checkout/onepage/review.phtml">
                            <block type="checkout/onepage_review_info" name="checkout.onepage.review.info" template="stenik/checkout/checkout/onepage/review/info.phtml">
                                <action method="addItemRender"><type>default</type><block>checkout/cart_item_renderer</block><template>stenik/checkout/checkout/onepage/review/item.phtml</template></action>
                                <action method="addItemRender"><type>grouped</type><block>checkout/cart_item_renderer_grouped</block><template>stenik/checkout/checkout/onepage/review/item.phtml</template></action>
                                <action method="addItemRender"><type>configurable</type><block>checkout/cart_item_renderer_configurable</block><template>stenik/checkout/checkout/onepage/review/item.phtml</template></action>
                                <block type="checkout/cart_totals" name="checkout.onepage.review.info.totals" as="totals" template="checkout/onepage/review/totals.phtml"/>
                                <block type="core/text_list" name="checkout.onepage.review.info.items.before" as="items_before" translate="label">
                                    <label>Items Before</label>
                                </block>
                                <block type="core/text_list" name="checkout.onepage.review.info.items.after" as="items_after" translate="label">
                                    <label>Items After</label>
                                </block>
                            </block>
                        </block>
                    </block>
                </block>

                <action method="addStep"><child>onestep</child></action>
            </block>
        </reference>

        <block type="core/text_list" name="checkout.onepage.billing.shipping_methods" as="shipping_methods">
            <block type="stenik_checkout/onepage_shipping_method_prechoose" name="checkout.onepage.shipping_method.prechoose" template="stenik/checkout/checkout/onepage/shipping_method/prechoose.phtml"/>
            <block type="checkout/onepage_shipping_method_additional" name="checkout.onepage.shipping_method.additional" as="additional" template="checkout/onepage/shipping_method/additional.phtml"/>
        </block>
    </checkout_onepage_index>

    <stenik_checkout_checkout_shipping_method_prechoose>
        <reference name="checkout.onepage.billing">
            <action method="setTemplate"><template>stenik/checkout/checkout/onepage/shipping_method/prechoose/billing.phtml</template></action>
            <action method="append"><block>checkout.onepage.billing.shipping_methods</block><alias>shipping_methods</alias></action>
        </reference>

        <reference name="checkout.onepage.shipping_method.available">
            <action method="setTemplate"><template>stenik/checkout/checkout/onepage/shipping_method/prechoose/available.phtml</template></action>
        </reference>
    </stenik_checkout_checkout_shipping_method_prechoose>

    <customer_address_form>
        <reference name="head">
            <action method="addItem"><type>skin_js</type><script>js/select2/select2.full.min.js</script><param>data-jquery</param></action>
            <action method="addItem"><type>skin_js</type><script>js/select2/i18n/bg.js</script><param>data-jquery</param></action>
            <action method="addItem"><type>skin_css</type><name>css/select2.min.css</name></action>
        </reference>

        <block type="stenik_checkout/onepage_shipping_method_prechoose" name="customer.address.edit.shipping_methods" as="shipping_methods" template="stenik/checkout/checkout/onepage/shipping_method/prechoose.phtml"/>
        <block type="core/text_list" name="customer.address.edit.custom_address_fields" as="custom_address_fields"/>
    </customer_address_form>

    <stenik_checkout_customer_address_prechoose>
        <reference name="customer_address_edit">
            <action method="setTemplate"><template>stenik/checkout/customer/shipping_method/prechoose/address/edit.phtml</template></action>
            <action method="append"><block>customer.address.edit.shipping_methods</block><alias>shipping_methods</alias></action>
            <action method="append"><block>customer.address.edit.custom_address_fields</block><alias>custom_address_fields</alias></action>
        </reference>
    </stenik_checkout_customer_address_prechoose>

    <stenik_checkout_partial_billing_address_update>
        <reference name="checkout.onepage.section.billing">
            <action method="addUpdateSectionGroupsOnFilledFields">
                <fields>
                    <address_select>billing-address-select</address_select>
                    <billing_country_id>billing:country_id</billing_country_id>
                    <billing_region_id>billing:region_id</billing_region_id>
                    <billing_region>billing:region</billing_region>
                    <billing_city>billing:city</billing_city>
                    <billing_postcode>billing:postcode</billing_postcode>
                    <!-- <billing_street_one>billing:street1</billing_street_one> -->
                </fields>
            </action>
        </reference>
    </stenik_checkout_partial_billing_address_update>

    <paypal_express_review>
        <reference name="head">
            <action method="addItem"><type>skin_js</type><name>js/jquery-1.10.2.min.js</name></action>
            <action method="addItem"><type>skin_js</type><name>js/colorbox.js</name></action>
        </reference>

        <reference name="paypal.express.review">
            <action method="setTemplate"><template>stenik/checkout/paypal/express/review.phtml</template></action>
            <action method="setUseAjax"><flag>0</flag></action>
            <block type="stenik_checkout/onepage_section" name="checkout.onepage.section.comment.agreements" template="stenik/checkout/checkout/onepage/section.phtml">
                <action method="setSectionGroup"><section>comment_agreements</section></action>
                <action method="setSectionCode"><section>comment_agreements</section></action>
                <action method="setSectionHtmlClass"><class>comment_agreements</class></action>
                <action method="setSectionPreserveValuesOnReload"><flag>1</flag></action>
                <block type="core/template" name="checkout.onepage.comment.agreements.wrapper" template="stenik/checkout/checkout/onepage/comment-agreements.phtml">
                    <block type="core/template" name="checkout.onepage.comment" as="comment" template="stenik/checkout/checkout/onepage/comment-agreements/comment.phtml"/>
                    <block type="checkout/agreements" name="checkout.onepage.agreements" as="agreements" template="stenik/checkout/checkout/onepage/comment-agreements/agreements.phtml"/>
                </block>
            </block>
        </reference>

        <reference name="express.review.billing">
            <action method="setTemplate"><template>stenik/checkout/paypal/express/review/address.phtml</template></action>
        </reference>

        <reference name="express.review.shipping">
            <action method="setTemplate"><template>stenik/checkout/paypal/express/review/address.phtml</template></action>
        </reference>
    </paypal_express_review>
</layout>