<?php
/**
 * PFG Analytics Traffic Template
 * 
 * This template displays the traffic analytics dashboard with native Magento styling
 * 
 * @package PFG_Analytics
 * <AUTHOR> Development Team
 */

// Get traffic data for display
$trafficData = $this->getTrafficData();
$comparisonData = $this->getComparisonTrafficData();
$filters = $this->getCurrentFilters();
$isComparisonEnabled = $this->isComparisonEnabled();
?>

<div class="content-header">
    <table cellspacing="0">
        <tr>
            <td><h3 class="icon-head head-report"><?php echo $this->__('Traffic Analytics Dashboard') ?></h3></td>
        </tr>
    </table>
</div>

<!-- Filter Form -->
<div class="entry-edit">
    <div class="entry-edit-head">
        <h4 class="icon-head head-edit-form fieldset-legend"><?php echo $this->__('Filter Options') ?></h4>
    </div>
    <div class="fieldset">
        <form id="traffic_filter_form" action="<?php echo $this->getFilterUrl() ?>" method="post">
            <input type="hidden" name="form_key" value="<?php echo Mage::getSingleton('core/session')->getFormKey() ?>" />

            <table cellspacing="0" class="form-list">
                <tbody>
                    <!-- Date Range - Vertical Layout -->
                    <tr>
                        <td class="label"><label for="from_date"><?php echo $this->__('From:') ?></label></td>
                        <td class="value">
                            <input type="text" id="from_date" name="from_date" value="<?php echo $filters['from_date'] ?>" class="input-text" readonly="readonly" style="width: 100px;" />
                            <img src="<?php echo $this->getSkinUrl('images/grid-cal.gif') ?>" id="from_date_trig" />
                        </td>
                        <td colspan="2"></td>
                    </tr>
                    <tr>
                        <td class="label"><label for="to_date"><?php echo $this->__('To:') ?></label></td>
                        <td class="value">
                            <input type="text" id="to_date" name="to_date" value="<?php echo $filters['to_date'] ?>" class="input-text" readonly="readonly" style="width: 100px;" />
                            <img src="<?php echo $this->getSkinUrl('images/grid-cal.gif') ?>" id="to_date_trig" />
                        </td>
                        <td colspan="2"></td>
                    </tr>

                    <!-- Store Filter -->
                    <tr>
                        <td class="label"><label for="store_id"><?php echo $this->__('Store:') ?></label></td>
                        <td class="value">
                            <select name="store_id" id="store_id" class="select" style="width: 200px;">
                                <?php foreach ($this->getStoreOptions() as $value => $label): ?>
                                    <option value="<?php echo $value ?>" <?php echo ($filters['store_id'] == $value) ? 'selected="selected"' : '' ?>>
                                        <?php echo $this->escapeHtml($label) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </td>
                        <td colspan="2"></td>
                    </tr>

                    <!-- Data Source Filter -->
                    <tr>
                        <td class="label"><label for="source"><?php echo $this->__('Data Source:') ?></label></td>
                        <td class="value">
                            <select name="source" id="source" class="select" style="width: 200px;">
                                <?php foreach ($this->getDataSourceOptions() as $value => $label): ?>
                                    <option value="<?php echo $value ?>" <?php echo ($filters['source'] == $value) ? 'selected="selected"' : '' ?>>
                                        <?php echo $this->escapeHtml($label) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </td>
                        <td colspan="2"></td>
                    </tr>

                    <!-- Comparison Mode -->
                    <tr>
                        <td class="label"><label for="comparison_mode"><?php echo $this->__('Comparison:') ?></label></td>
                        <td class="value">
                            <select name="comparison_mode" id="comparison_mode" class="select" style="width: 200px;">
                                <?php foreach ($this->getComparisonModeOptions() as $value => $label): ?>
                                    <option value="<?php echo $value ?>" <?php echo ($filters['comparison_mode'] == $value) ? 'selected="selected"' : '' ?>>
                                        <?php echo $this->escapeHtml($label) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </td>
                        <td colspan="2"></td>
                    </tr>

                    <!-- Chart Grouping -->
                    <tr>
                        <td class="label"><label for="grouping"><?php echo $this->__('Grouping:') ?></label></td>
                        <td class="value">
                            <select name="grouping" id="grouping" class="select" style="width: 200px;">
                                <?php foreach ($this->getGroupingOptions() as $value => $label): ?>
                                    <option value="<?php echo $value ?>" <?php echo ($filters['grouping'] == $value) ? 'selected="selected"' : '' ?>>
                                        <?php echo $this->escapeHtml($label) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </td>
                        <td colspan="2"></td>
                    </tr>

                    <!-- Submit Button -->
                    <tr>
                        <td class="label">&nbsp;</td>
                        <td class="value">
                            <button type="submit" class="scalable" onclick="return false;">
                                <span><?php echo $this->__('Apply Filters') ?></span>
                            </button>
                            <button type="button" class="scalable" onclick="setQuickDateRange(30);">
                                <span><?php echo $this->__('Last 30 Days') ?></span>
                            </button>
                            <button type="button" class="scalable" onclick="setQuickDateRange(90);">
                                <span><?php echo $this->__('Last 90 Days') ?></span>
                            </button>
                        </td>
                        <td colspan="2"></td>
                    </tr>
                </tbody>
            </table>
        </form>
    </div>
</div>

<!-- Traffic Summary Cards -->
<?php if (isset($trafficData['error'])): ?>
    <div class="entry-edit">
        <div class="entry-edit-head">
            <h4 class="icon-head head-edit-form fieldset-legend"><?php echo $this->__('Error') ?></h4>
        </div>
        <div class="fieldset">
            <p class="error"><?php echo $this->escapeHtml($trafficData['error']) ?></p>
        </div>
    </div>
<?php else: ?>
    <!-- Summary Cards -->
    <div class="entry-edit">
        <div class="entry-edit-head">
            <h4 class="icon-head head-edit-form fieldset-legend"><?php echo $this->__('Traffic Summary') ?></h4>
        </div>
        <div class="fieldset">
            <table cellspacing="0" class="form-list">
                <tbody>
                    <tr>
                        <!-- Total Visits -->
                        <td class="label" style="width: 200px; vertical-align: top;">
                            <div style="background: #f8f8f8; padding: 15px; border: 1px solid #ddd; text-align: center;">
                                <h3 style="margin: 0; color: #333;"><?php echo $this->formatTrafficNumber($trafficData['total_visits']) ?></h3>
                                <p style="margin: 5px 0 0 0; color: #666;"><?php echo $this->__('Total Visits') ?></p>
                                <?php if ($isComparisonEnabled && $comparisonData): ?>
                                    <?php $change = $this->calculateChange($trafficData['total_visits'], $comparisonData['total_visits']) ?>
                                    <p style="margin: 5px 0 0 0; color: <?php echo $change['direction'] == 'up' ? 'green' : ($change['direction'] == 'down' ? 'red' : '#666') ?>;">
                                        <?php echo $change['formatted'] ?>
                                    </p>
                                <?php endif; ?>
                            </div>
                        </td>
                        
                        <!-- Unique Visitors -->
                        <td class="label" style="width: 200px; vertical-align: top;">
                            <div style="background: #f8f8f8; padding: 15px; border: 1px solid #ddd; text-align: center;">
                                <h3 style="margin: 0; color: #333;"><?php echo $this->formatTrafficNumber($trafficData['total_unique_visitors']) ?></h3>
                                <p style="margin: 5px 0 0 0; color: #666;"><?php echo $this->__('Unique Visitors') ?></p>
                                <?php if ($isComparisonEnabled && $comparisonData): ?>
                                    <?php $change = $this->calculateChange($trafficData['total_unique_visitors'], $comparisonData['total_unique_visitors']) ?>
                                    <p style="margin: 5px 0 0 0; color: <?php echo $change['direction'] == 'up' ? 'green' : ($change['direction'] == 'down' ? 'red' : '#666') ?>;">
                                        <?php echo $change['formatted'] ?>
                                    </p>
                                <?php endif; ?>
                            </div>
                        </td>
                        
                        <!-- Page Views -->
                        <td class="label" style="width: 200px; vertical-align: top;">
                            <div style="background: #f8f8f8; padding: 15px; border: 1px solid #ddd; text-align: center;">
                                <h3 style="margin: 0; color: #333;"><?php echo $this->formatTrafficNumber($trafficData['total_page_views']) ?></h3>
                                <p style="margin: 5px 0 0 0; color: #666;"><?php echo $this->__('Page Views') ?></p>
                                <?php if ($isComparisonEnabled && $comparisonData): ?>
                                    <?php $change = $this->calculateChange($trafficData['total_page_views'], $comparisonData['total_page_views']) ?>
                                    <p style="margin: 5px 0 0 0; color: <?php echo $change['direction'] == 'up' ? 'green' : ($change['direction'] == 'down' ? 'red' : '#666') ?>;">
                                        <?php echo $change['formatted'] ?>
                                    </p>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td>&nbsp;</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Traffic Chart -->
    <div class="entry-edit">
        <div class="entry-edit-head">
            <h4 class="icon-head head-edit-form fieldset-legend"><?php echo $this->__('Traffic Chart') ?></h4>
        </div>
        <div class="fieldset">
            <div id="traffic_chart_container" style="width: 100%; height: 400px; position: relative;">
                <canvas id="traffic_chart" width="800" height="400"></canvas>
                <div id="chart_loading" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">
                    <?php echo $this->__('Loading chart data...') ?>
                </div>
            </div>
            
            <!-- Chart Controls -->
            <div style="margin-top: 15px; text-align: center;">
                <label>
                    <input type="checkbox" id="show_visits" checked="checked" onchange="toggleChartLine('visits');" />
                    <?php echo $this->__('Show Visits') ?>
                </label>
                &nbsp;&nbsp;
                <label>
                    <input type="checkbox" id="show_unique_visitors" checked="checked" onchange="toggleChartLine('unique_visitors');" />
                    <?php echo $this->__('Show Unique Visitors') ?>
                </label>
                &nbsp;&nbsp;
                <label>
                    <input type="checkbox" id="show_page_views" checked="checked" onchange="toggleChartLine('page_views');" />
                    <?php echo $this->__('Show Page Views') ?>
                </label>
            </div>
        </div>
    </div>
<?php endif; ?>

<script type="text/javascript">
// Chart configuration
var trafficChartConfig = <?php echo $this->getChartConfigJson() ?>;
var trafficChart = null;

// Initialize when DOM is ready
document.observe('dom:loaded', function() {
    initializeDatePickers();
    initializeTrafficChart();
    setupFormHandlers();
});

// Initialize date pickers
function initializeDatePickers() {
    Calendar.setup({
        inputField: 'from_date',
        ifFormat: '%Y-%m-%d',
        button: 'from_date_trig',
        align: 'Bl',
        singleClick: true
    });
    
    Calendar.setup({
        inputField: 'to_date',
        ifFormat: '%Y-%m-%d',
        button: 'to_date_trig',
        align: 'Bl',
        singleClick: true
    });
}

// Set quick date range
function setQuickDateRange(days) {
    var today = new Date();
    var fromDate = new Date();
    fromDate.setDate(today.getDate() - days);
    
    $('from_date').value = fromDate.getFullYear() + '-' + 
        String(fromDate.getMonth() + 1).padStart(2, '0') + '-' + 
        String(fromDate.getDate()).padStart(2, '0');
    $('to_date').value = today.getFullYear() + '-' + 
        String(today.getMonth() + 1).padStart(2, '0') + '-' + 
        String(today.getDate()).padStart(2, '0');
}

// Setup form handlers
function setupFormHandlers() {
    $('traffic_filter_form').observe('submit', function(e) {
        e.preventDefault();
        loadTrafficChart();
        return false;
    });
    
    // Auto-submit on filter changes
    ['store_id', 'source', 'comparison_mode', 'grouping'].each(function(fieldId) {
        if ($(fieldId)) {
            $(fieldId).observe('change', function() {
                loadTrafficChart();
            });
        }
    });
}

// Initialize traffic chart
function initializeTrafficChart() {
    loadTrafficChart();
}

// Load traffic chart data
function loadTrafficChart() {
    var params = {
        from_date: $('from_date').value,
        to_date: $('to_date').value,
        store_id: $('store_id').value,
        source: $('source').value,
        comparison_mode: $('comparison_mode').value,
        grouping: $('grouping').value
    };
    
    $('chart_loading').show();
    
    new Ajax.Request(trafficChartConfig.dataUrl, {
        method: 'post',
        parameters: params,
        onSuccess: function(response) {
            $('chart_loading').hide();
            var data = response.responseJSON;
            if (data.success) {
                renderTrafficChart(data);
            } else {
                showChartError(data.error || 'Unknown error occurred');
            }
        },
        onFailure: function() {
            $('chart_loading').hide();
            showChartError('Failed to load chart data');
        }
    });
}

// Render traffic chart (placeholder - would need actual chart library)
function renderTrafficChart(data) {
    // This would integrate with a chart library like Chart.js
    console.log('Chart data:', data);
    
    // For now, just show a message
    var canvas = $('traffic_chart');
    var ctx = canvas.getContext('2d');
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    ctx.fillStyle = '#333';
    ctx.font = '16px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('Chart integration pending - data loaded successfully', canvas.width/2, canvas.height/2);
}

// Show chart error
function showChartError(message) {
    var canvas = $('traffic_chart');
    var ctx = canvas.getContext('2d');
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    ctx.fillStyle = 'red';
    ctx.font = '16px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('Error: ' + message, canvas.width/2, canvas.height/2);
}

// Toggle chart line visibility
function toggleChartLine(lineType) {
    // This would integrate with the chart library to show/hide lines
    console.log('Toggle line:', lineType);
}
</script>
