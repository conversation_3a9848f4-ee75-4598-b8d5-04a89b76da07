<?php
/**
 * PFG Analytics Template
 * 
 * This template displays the analytics dashboard with native Magento styling
 * 
 * @package PFG_Analytics
 * <AUTHOR> Development Team
 */

// Get sales data for display
$salesData = $this->getSalesData();
$chartData = $this->getChartData();
$statusMappings = $this->getStatusMappings();
$selectedStatuses = $this->getSelectedStatuses();
$comparisonMode = $this->getComparisonMode();
$chartGrouping = $this->getChartGrouping();

// Note: External CSS and JS files removed as they don't exist
// All styling and JavaScript is now inline in this template
?>

<div class="content-header">
    <table cellspacing="0">
        <tr>
            <td><h3 class="icon-head head-report"><?php echo $this->__('PFG Analytics') ?></h3></td>
        </tr>
    </table>
</div>

<!-- Filter Form -->
<div class="entry-edit">
    <div class="entry-edit-head">
        <h4 class="icon-head head-edit-form fieldset-legend"><?php echo $this->__('Filter Options') ?></h4>
    </div>
    <div class="fieldset">
        <form id="filter_form" action="<?php echo $this->getFilterUrl() ?>" method="post">
            <input type="hidden" name="form_key" value="<?php echo Mage::getSingleton('core/session')->getFormKey() ?>" />
            <input type="hidden" name="chart_grouping" id="hidden_chart_grouping" value="<?php echo $this->getChartGrouping() ?>" />

            <table cellspacing="0" class="form-list">
                <tbody>
                    <!-- Date Range - Vertical Layout -->
                    <tr>
                        <td class="label"><label for="from_date"><?php echo $this->__('From:') ?></label></td>
                        <td class="value">
                            <input type="text" id="from_date" name="from_date" value="<?php echo $this->getFromDate() ?>" class="input-text" readonly="readonly" style="width: 100px;" />
                            <img src="<?php echo $this->getSkinUrl('images/grid-cal.gif') ?>" id="from_date_trig" />
                        </td>
                        <td colspan="2"></td>
                    </tr>
                    <tr>
                        <td class="label"><label for="to_date"><?php echo $this->__('To:') ?></label></td>
                        <td class="value">
                            <input type="text" id="to_date" name="to_date" value="<?php echo $this->getToDate() ?>" class="input-text" readonly="readonly" style="width: 100px;" />
                            <img src="<?php echo $this->getSkinUrl('images/grid-cal.gif') ?>" id="to_date_trig" />
                        </td>
                        <td colspan="2"></td>
                    </tr>
                    
                    <!-- Comparison Mode -->
                    <tr>
                        <td class="label"><label for="comparison_mode"><?php echo $this->__('Comparison:') ?></label></td>
                        <td class="value">
                            <select id="comparison_mode" name="comparison_mode" class="select" onchange="toggleComparisonDates()">
                                <?php 
                                $currentComparison = $this->getComparisonMode();
                                foreach ($this->getComparisonModes() as $mode => $label): 
                                ?>
                                    <option value="<?php echo $mode ?>" <?php echo ($currentComparison === $mode) ? 'selected="selected"' : '' ?>>
                                        <?php echo $label ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </td>
                        <td colspan="2"></td>
                    </tr>



                    <!-- Order Statuses -->
                    <tr>
                        <td class="label"><label for="order_statuses"><?php echo $this->__('Order Statuses:') ?></label></td>
                        <td class="value" colspan="3">
                            <select id="order_statuses" name="order_statuses[]" multiple="multiple" class="multiselect">
                                <?php
                                $selectedStatuses = $this->getSelectedStatuses();
                                $allStatuses = $this->getOrderStatuses();
                                ?>
                                <option value="" <?php echo empty($selectedStatuses) ? 'selected="selected"' : '' ?>>
                                    <?php echo $this->__('All Statuses') ?>
                                </option>
                                <?php foreach ($allStatuses as $statusCode => $statusLabel): ?>
                                    <option value="<?php echo $statusCode ?>"
                                            <?php echo in_array($statusCode, $selectedStatuses) ? 'selected="selected"' : '' ?>>
                                        <?php echo $statusLabel ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </td>
                    </tr>

                    <!-- Submit Button -->
                    <tr>
                        <td class="label"></td>
                        <td class="value" colspan="3">
                            <button type="submit" class="scalable save"><?php echo $this->__('Apply Filters') ?></button>
                        </td>
                    </tr>

                    <!-- Quick Filters -->
                    <tr>
                        <td class="label"><?php echo $this->__('Quick Filters:') ?></td>
                        <td class="value" colspan="3">
                            <button type="button" class="scalable" onclick="applyQuickFilter('current_month')"><?php echo $this->__('Current Month') ?></button>
                            <button type="button" class="scalable" onclick="applyQuickFilter('last_month')"><?php echo $this->__('Last Month') ?></button>
                            <button type="button" class="scalable" onclick="applyQuickFilter('30days')"><?php echo $this->__('Last 30 Days') ?></button>
                            <button type="button" class="scalable" onclick="applyQuickFilter('90days')"><?php echo $this->__('Last 90 Days') ?></button>
                            <button type="button" class="scalable" onclick="applyQuickFilter('ytd')"><?php echo $this->__('Year to Date') ?></button>
                            <button type="button" class="scalable" onclick="applyQuickFilter('q1')"><?php echo $this->__('Q1 (Jan-Mar)') ?></button>
                            <button type="button" class="scalable" onclick="applyQuickFilter('q2')"><?php echo $this->__('Q2 (Apr-Jun)') ?></button>
                            <button type="button" class="scalable" onclick="applyQuickFilter('q3')"><?php echo $this->__('Q3 (Jul-Sep)') ?></button>
                            <button type="button" class="scalable" onclick="applyQuickFilter('q4')"><?php echo $this->__('Q4 (Oct-Dec)') ?></button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </form>
    </div>
</div>



<!-- Sales Statistics -->
<div class="entry-edit">
    <div class="entry-edit-head">
        <h4 class="icon-head head-edit-form fieldset-legend"><?php echo $this->__('Sales Statistics') ?></h4>
    </div>
    <div class="fieldset sales-stats-section">
        <?php if ($salesData['total_orders'] > 0): ?>
            <div class="grid">
                <table cellspacing="0" class="data">
                    <thead>
                        <tr class="headings">
                            <th><?php echo $this->__('Metric') ?></th>
                            <th><?php echo $this->__('Current Period') ?></th>
                            <?php if (isset($salesData['comparison'])): ?>
                                <th><?php echo $this->__('Previous Period') ?></th>
                                <th><?php echo $this->__('Change') ?></th>
                            <?php endif; ?>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><?php echo $this->__('Total Orders') ?></td>
                            <td><?php echo number_format($salesData['total_orders']) ?></td>
                            <?php if (isset($salesData['comparison'])): ?>
                                <td><?php echo number_format($salesData['comparison']['total_orders']) ?></td>
                                <td>
                                    <?php
                                    $change = $salesData['total_orders_change'];
                                    $class = $change['direction'] === 'up' ? 'notice' : ($change['direction'] === 'down' ? 'error' : '');
                                    $icon = $change['direction'] === 'up' ? '↑' : ($change['direction'] === 'down' ? '↓' : '→');
                                    ?>
                                    <span class="<?php echo $class ?>">
                                        <?php echo $icon ?> <?php echo $change['percentage'] ?>%
                                        <?php if ($change['is_new']): ?>
                                            <em><?php echo $this->__('New!') ?></em>
                                        <?php endif; ?>
                                    </span>
                                </td>
                            <?php endif; ?>
                        </tr>
                        <tr>
                            <td><?php echo $this->__('Total Amount') ?></td>
                            <td><?php echo $this->formatCurrency($salesData['total_amount']) ?></td>
                            <?php if (isset($salesData['comparison'])): ?>
                                <td><?php echo $this->formatCurrency($salesData['comparison']['total_amount']) ?></td>
                                <td>
                                    <?php
                                    $change = $salesData['total_amount_change'];
                                    $class = $change['direction'] === 'up' ? 'notice' : ($change['direction'] === 'down' ? 'error' : '');
                                    $icon = $change['direction'] === 'up' ? '↑' : ($change['direction'] === 'down' ? '↓' : '→');
                                    ?>
                                    <span class="<?php echo $class ?>">
                                        <?php echo $icon ?> <?php echo $change['percentage'] ?>%
                                        <?php if ($change['is_new']): ?>
                                            <em><?php echo $this->__('New!') ?></em>
                                        <?php endif; ?>
                                    </span>
                                </td>
                            <?php endif; ?>
                        </tr>
                        <tr>
                            <td><?php echo $this->__('Average Order Amount') ?></td>
                            <td><?php echo $this->formatCurrency($salesData['average_amount']) ?></td>
                            <?php if (isset($salesData['comparison'])): ?>
                                <td><?php echo $this->formatCurrency($salesData['comparison']['average_amount']) ?></td>
                                <td>
                                    <?php
                                    $change = $salesData['average_amount_change'];
                                    $class = $change['direction'] === 'up' ? 'notice' : ($change['direction'] === 'down' ? 'error' : '');
                                    $icon = $change['direction'] === 'up' ? '↑' : ($change['direction'] === 'down' ? '↓' : '→');
                                    ?>
                                    <span class="<?php echo $class ?>">
                                        <?php echo $icon ?> <?php echo $change['percentage'] ?>%
                                        <?php if ($change['is_new']): ?>
                                            <em><?php echo $this->__('New!') ?></em>
                                        <?php endif; ?>
                                    </span>
                                </td>
                            <?php endif; ?>
                        </tr>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <p class="note">
                <span><?php echo $this->__('No orders found for the selected date range.') ?></span>
            </p>
        <?php endif; ?>
    </div>
</div>

<!-- Daily Purchases Chart -->
<?php if ($this->isValidDateRange()): ?>
    <div class="entry-edit">
        <div class="entry-edit-head">
            <h4 class="icon-head head-edit-form fieldset-legend"><?php echo $this->__('Sales Chart') ?></h4>
        </div>
        <div class="fieldset">
            <!-- Chart Controls -->
            <div class="chart-controls" style="margin-bottom: 15px; padding: 10px; background: #f8f8f8; border: 1px solid #ddd;">
                <div style="display: inline-block; margin-right: 20px;">
                    <label for="chart_grouping" style="font-weight: bold; margin-right: 10px;"><?php echo $this->__('Chart Grouping:') ?></label>
                    <select id="chart_grouping" name="chart_grouping" class="select" style="padding: 4px 8px; border: 1px solid #ccc;">
                        <?php
                        $currentGrouping = $this->getChartGrouping();
                        foreach ($this->getChartGroupingOptions() as $grouping => $label):
                        ?>
                            <option value="<?php echo $grouping ?>" <?php echo ($currentGrouping === $grouping) ? 'selected="selected"' : '' ?>>
                                <?php echo $label ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div style="display: inline-block; margin-right: 20px;">
                    <label for="purchases_toggle" style="font-weight: bold; margin-right: 10px;"><?php echo $this->__('Purchases:') ?></label>
                    <input type="checkbox" id="purchases_toggle" name="purchases_toggle" <?php echo $this->getIncludePurchases() ? 'checked="checked"' : '' ?> onchange="togglePurchases()" />
                    <label for="purchases_toggle" style="font-weight: normal; margin-left: 5px;"><?php echo $this->__('Show Purchase Count') ?></label>
                </div>

                <div style="display: inline-block;">
                    <label for="revenue_toggle" style="font-weight: bold; margin-right: 10px;"><?php echo $this->__('Revenue:') ?></label>
                    <input type="checkbox" id="revenue_toggle" name="revenue_toggle" <?php echo $this->getIncludeRevenue() ? 'checked="checked"' : '' ?> onchange="toggleRevenue()" />
                    <label for="revenue_toggle" style="font-weight: normal; margin-left: 5px;"><?php echo $this->__('Show Revenue Line') ?></label>
                </div>
            </div>

            <div id="chart-container" style="height: 400px; position: relative;">
                <canvas id="purchasesChart" width="800" height="400"></canvas>
            </div>
        </div>
    </div>
<?php else: ?>
    <div class="entry-edit">
        <div class="entry-edit-head">
            <h4 class="icon-head head-edit-form fieldset-legend"><?php echo $this->__('Daily Purchases Chart') ?></h4>
        </div>
        <div class="fieldset">
            <p class="note">
                <span><?php echo $this->getDateRangeWarning() ?></span>
            </p>
        </div>
    </div>
<?php endif; ?>

<!-- Order Status Breakdown -->
<?php if ($salesData['total_orders'] > 0): ?>
    <div class="entry-edit">
        <div class="entry-edit-head">
            <h4 class="icon-head head-edit-form fieldset-legend"><?php echo $this->__('Order Status Breakdown') ?></h4>
        </div>
        <div class="fieldset">
            <div class="grid">
                <table cellspacing="0" class="data">
                    <thead>
                        <tr class="headings">
                            <th><?php echo $this->__('Order Status') ?></th>
                            <th><?php echo $this->__('Count') ?></th>
                            <th><?php echo $this->__('Percentage') ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        $mappedBreakdown = $this->getMappedStatusBreakdown($salesData['status_breakdown']);



                        foreach ($mappedBreakdown as $statusName => $statusData):
                            $percentage = ($statusData['count'] / $salesData['total_orders']) * 100;

                            // Check if this is a mapped status with breakdown data
                            $breakdown = isset($statusData['breakdown']) ? $statusData['breakdown'] : array($statusName => $statusData['count']);
                            // Show breakdown for any mapped status (even single status mappings show the real status name)
                            $hasBreakdown = isset($statusData['breakdown']) && !empty($statusData['breakdown']);
                            $rowId = 'status-row-' . md5($statusName);

                            // Get localized status label for main status
                            $orderConfig = Mage::getSingleton('sales/order_config');
                            $displayStatusName = $statusName;

                            // For unmapped statuses or single real status mappings, use Magento's native label
                            if (isset($statusData['real_statuses']) && count($statusData['real_statuses']) == 1) {
                                $realStatus = $statusData['real_statuses'][0];
                                $magentoLabel = $orderConfig->getStatusLabel($realStatus);
                                if (!empty($magentoLabel)) {
                                    $displayStatusName = $magentoLabel;
                                }
                            }
                            // For custom mappings with multiple statuses, keep the custom name but it should already be properly set
                        ?>
                            <tr>
                                <td>
                                    <div class="status-pill-container">
                                        <span class="status-pill main-pill" onclick="<?php echo $hasBreakdown ? "toggleStatusBreakdown('{$rowId}')" : '' ?>">
                                            <span class="pill-label"><?php echo htmlspecialchars($displayStatusName) ?></span>
                                            <span class="pill-count"><?php echo number_format($statusData['count']) ?></span>
                                            <?php if ($hasBreakdown): ?>
                                                <span class="pill-dropdown-arrow">▼</span>
                                            <?php endif; ?>
                                        </span>

                                        <?php if ($hasBreakdown): ?>
                                            <div id="<?php echo $rowId ?>-breakdown" class="status-breakdown-dropdown" style="display: none;">
                                                <div class="breakdown-pills">
                                                    <?php foreach ($breakdown as $realStatus => $count):
                                                        // Get localized label for each real status in breakdown
                                                        $realStatusLabel = $orderConfig->getStatusLabel($realStatus);
                                                        if (empty($realStatusLabel)) {
                                                            $realStatusLabel = $realStatus;
                                                        }
                                                    ?>
                                                        <span class="status-pill breakdown-pill">
                                                            <span class="pill-label"><?php echo htmlspecialchars($realStatusLabel) ?></span>
                                                            <span class="pill-count"><?php echo number_format($count) ?></span>
                                                        </span>
                                                    <?php endforeach; ?>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td><?php echo number_format($statusData['count']) ?></td>
                                <td><?php echo number_format($percentage, 1) ?>%</td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
<?php endif; ?>

<!-- Top Selling Analytics Dashboard -->
<div class="entry-edit">
    <div class="entry-edit-head">
        <h4 class="icon-head head-edit-form fieldset-legend"><?php echo $this->__('Top Selling Analytics Dashboard') ?></h4>
    </div>
    <div class="fieldset">
        <div class="top-selling-dashboard" style="display: flex; gap: 20px; flex-wrap: wrap;">

            <!-- Column 1: Top Selling Categories -->
            <div class="analytics-column" style="flex: 1; min-width: 300px;">
                <div class="grid">
                    <table cellspacing="0" class="data">
                        <thead>
                            <tr class="headings">
                                <th colspan="3" style="text-align: center; font-size: 14px; padding: 12px;">
                                    <?php echo $this->__('Top Selling Categories') ?>
                                </th>
                            </tr>
                            <tr class="headings">
                                <th><?php echo $this->__('Category Name') ?></th>
                                <th><?php echo $this->__('Quantity Sold') ?></th>
                                <th><?php echo $this->__('Percentage') ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $categoriesData = $this->getTopSellingCategoriesData();
                            if (!empty($categoriesData) && !isset($categoriesData['error'])):
                                foreach ($categoriesData as $category):
                                    $isTotal = isset($category['is_total_row']) && $category['is_total_row'];
                                    $rowClass = $isTotal ? 'total-row' : '';
                                    $rowStyle = $isTotal ? 'font-weight: bold; background-color: #f8f8f8; border-top: 2px solid #ddd;' : '';
                            ?>
                                <tr class="<?php echo $rowClass ?>" style="<?php echo $rowStyle ?>">
                                    <td><?php echo htmlspecialchars($category['category_name']) ?></td>
                                    <td><?php echo number_format($category['total_quantity']) ?></td>
                                    <td><?php echo $category['percentage'] ?>%</td>
                                </tr>
                            <?php
                                endforeach;
                            else:
                            ?>
                                <tr>
                                    <td colspan="3" style="text-align: center; padding: 20px; color: #666;">
                                        <?php
                                        if (isset($categoriesData['error'])) {
                                            echo htmlspecialchars($categoriesData['error']);
                                        } else {
                                            echo $this->__('No category data available for the selected date range and filters.');
                                        }
                                        ?>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Column 2: Top Selling Brands -->
            <div class="analytics-column" style="flex: 1; min-width: 300px;">
                <div class="grid">
                    <table cellspacing="0" class="data">
                        <thead>
                            <tr class="headings">
                                <th colspan="3" style="text-align: center; font-size: 14px; padding: 12px;">
                                    <?php echo $this->__('Top Selling Brands') ?>
                                </th>
                            </tr>
                            <tr class="headings">
                                <th><?php echo $this->__('Brand Name') ?></th>
                                <th><?php echo $this->__('Quantity Sold') ?></th>
                                <th><?php echo $this->__('Percentage') ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $brandsData = $this->getTopSellingBrandsData();
                            if (!empty($brandsData) && !isset($brandsData['error'])):
                                foreach ($brandsData as $brand):
                                    $isTotal = isset($brand['is_total_row']) && $brand['is_total_row'];
                                    $rowClass = $isTotal ? 'total-row' : '';
                                    $rowStyle = $isTotal ? 'font-weight: bold; background-color: #f8f8f8; border-top: 2px solid #ddd;' : '';
                            ?>
                                <tr class="<?php echo $rowClass ?>" style="<?php echo $rowStyle ?>">
                                    <td><?php echo htmlspecialchars($brand['brand_name']) ?></td>
                                    <td><?php echo number_format($brand['total_quantity']) ?></td>
                                    <td><?php echo $brand['percentage'] ?>%</td>
                                </tr>
                            <?php
                                endforeach;
                            else:
                            ?>
                                <tr>
                                    <td colspan="3" style="text-align: center; padding: 20px; color: #666;">
                                        <?php
                                        if (isset($brandsData['error'])) {
                                            echo htmlspecialchars($brandsData['error']);
                                        } else {
                                            echo $this->__('No brand data available for the selected date range and filters.');
                                        }
                                        ?>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Column 3: Top Selling Products -->
            <div class="analytics-column" style="flex: 1; min-width: 300px;">
                <div class="grid">
                    <table cellspacing="0" class="data">
                        <thead>
                            <tr class="headings">
                                <th colspan="3" style="text-align: center; font-size: 14px; padding: 12px;">
                                    <?php echo $this->__('Top Selling Products') ?>
                                </th>
                            </tr>
                            <tr class="headings">
                                <th><?php echo $this->__('Product') ?></th>
                                <th><?php echo $this->__('Quantity Sold') ?></th>
                                <th><?php echo $this->__('Percentage') ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $productsData = $this->getTopSellingProductsData();
                            if (!empty($productsData) && !isset($productsData['error'])):
                                foreach ($productsData as $product):
                                    $isTotal = isset($product['is_total_row']) && $product['is_total_row'];
                                    $rowClass = $isTotal ? 'total-row' : '';
                                    $rowStyle = $isTotal ? 'font-weight: bold; background-color: #f8f8f8; border-top: 2px solid #ddd;' : '';
                            ?>
                                <tr class="<?php echo $rowClass ?>" style="<?php echo $rowStyle ?>">
                                    <td><?php echo htmlspecialchars($product['product_display']) ?></td>
                                    <td><?php echo number_format($product['total_quantity']) ?></td>
                                    <td><?php echo $product['percentage'] ?>%</td>
                                </tr>
                            <?php
                                endforeach;
                            else:
                            ?>
                                <tr>
                                    <td colspan="3" style="text-align: center; padding: 20px; color: #666;">
                                        <?php
                                        if (isset($productsData['error'])) {
                                            echo htmlspecialchars($productsData['error']);
                                        } else {
                                            echo $this->__('No product data available for the selected date range and filters.');
                                        }
                                        ?>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript -->
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script type="text/javascript">
// Calendar setup
Calendar.setup({
    inputField: 'from_date',
    ifFormat: '%Y-%m-%d',
    button: 'from_date_trig',
    align: 'Bl',
    singleClick: true
});

Calendar.setup({
    inputField: 'to_date',
    ifFormat: '%Y-%m-%d',
    button: 'to_date_trig',
    align: 'Bl',
    singleClick: true
});

// Toggle comparison date fields (no longer needed but keeping for compatibility)
function toggleComparisonDates() {
    // No action needed since we removed custom comparison date fields
}

// Toggle status breakdown display
function toggleStatusBreakdown(rowId) {
    var breakdown = document.getElementById(rowId + '-breakdown');
    var toggleButton = document.querySelector('button[onclick*="' + rowId + '"] .toggle-icon');

    if (breakdown.style.display === 'none' || breakdown.style.display === '') {
        breakdown.style.display = 'block';
        if (toggleButton) toggleButton.innerHTML = '▲';
    } else {
        breakdown.style.display = 'none';
        if (toggleButton) toggleButton.innerHTML = '▼';
    }
}

// Apply quick filter
function applyQuickFilter(filter) {
    var quickFilterDates = <?php echo json_encode(array(
        'current_month' => $this->getQuickFilterDates('current_month'),
        'last_month' => $this->getQuickFilterDates('last_month'),
        '30days' => $this->getQuickFilterDates('30days'),
        '90days' => $this->getQuickFilterDates('90days'),
        'ytd' => $this->getQuickFilterDates('ytd'),
        'q1' => $this->getQuickFilterDates('q1'),
        'q2' => $this->getQuickFilterDates('q2'),
        'q3' => $this->getQuickFilterDates('q3'),
        'q4' => $this->getQuickFilterDates('q4')
    )); ?>;

    if (quickFilterDates[filter]) {
        document.getElementById('from_date').value = quickFilterDates[filter].from;
        document.getElementById('to_date').value = quickFilterDates[filter].to;
        document.getElementById('filter_form').submit();
    }
}

// Toggle purchases count display
function togglePurchases() {
    var purchasesToggle = document.getElementById('purchases_toggle');
    var revenueToggle = document.getElementById('revenue_toggle');
    var form = document.getElementById('filter_form');

    // Add hidden field for purchases toggle
    var hiddenPurchases = document.getElementById('hidden_include_purchases');
    if (!hiddenPurchases) {
        hiddenPurchases = document.createElement('input');
        hiddenPurchases.type = 'hidden';
        hiddenPurchases.name = 'include_purchases';
        hiddenPurchases.id = 'hidden_include_purchases';
        form.appendChild(hiddenPurchases);
    }

    // Add hidden field for revenue toggle to preserve its state
    var hiddenRevenue = document.getElementById('hidden_include_revenue');
    if (!hiddenRevenue) {
        hiddenRevenue = document.createElement('input');
        hiddenRevenue.type = 'hidden';
        hiddenRevenue.name = 'include_revenue';
        hiddenRevenue.id = 'hidden_include_revenue';
        form.appendChild(hiddenRevenue);
    }

    // Set both values to preserve state
    hiddenPurchases.value = purchasesToggle.checked ? '1' : '0';
    hiddenRevenue.value = revenueToggle.checked ? '1' : '0';
    form.submit();
}

// Toggle revenue display
function toggleRevenue() {
    var purchasesToggle = document.getElementById('purchases_toggle');
    var revenueToggle = document.getElementById('revenue_toggle');
    var form = document.getElementById('filter_form');

    // Add hidden field for purchases toggle to preserve its state
    var hiddenPurchases = document.getElementById('hidden_include_purchases');
    if (!hiddenPurchases) {
        hiddenPurchases = document.createElement('input');
        hiddenPurchases.type = 'hidden';
        hiddenPurchases.name = 'include_purchases';
        hiddenPurchases.id = 'hidden_include_purchases';
        form.appendChild(hiddenPurchases);
    }

    // Add hidden field for revenue toggle
    var hiddenRevenue = document.getElementById('hidden_include_revenue');
    if (!hiddenRevenue) {
        hiddenRevenue = document.createElement('input');
        hiddenRevenue.type = 'hidden';
        hiddenRevenue.name = 'include_revenue';
        hiddenRevenue.id = 'hidden_include_revenue';
        form.appendChild(hiddenRevenue);
    }

    // Set both values to preserve state
    hiddenPurchases.value = purchasesToggle.checked ? '1' : '0';
    hiddenRevenue.value = revenueToggle.checked ? '1' : '0';
    form.submit();
}

// Chart initialization
<?php if ($this->isValidDateRange() && $salesData['total_orders'] > 0): ?>
var purchasesChart = null;

function renderPurchasesChart() {
    var chartData = <?php echo $this->getChartDataJson() ?>;
    var ctx = document.getElementById('purchasesChart').getContext('2d');

    // Debug logging
    console.log('Chart data:', chartData);

    // Destroy existing chart if it exists
    if (purchasesChart) {
        purchasesChart.destroy();
    }

    // EDGE CASE HANDLING: Comprehensive chart data validation
    if (!chartData) {
        console.error('No chart data provided');
        document.getElementById('chart-container').innerHTML =
            '<div class="notice-msg"><span>No chart data available. Please check your date range and filters.</span></div>';
        return;
    }

    if (!chartData.primary) {
        console.error('No primary chart data available');
        document.getElementById('chart-container').innerHTML =
            '<div class="notice-msg"><span>No primary data available for the selected period.</span></div>';
        return;
    }

    if (!chartData.primary.labels || chartData.primary.labels.length === 0) {
        console.error('No chart labels available');
        document.getElementById('chart-container').innerHTML =
            '<div class="notice-msg"><span>No data points available for the selected date range.</span></div>';
        return;
    }

    if (!chartData.primary.data && !chartData.primary.revenue) {
        console.error('No chart data or revenue available');
        document.getElementById('chart-container').innerHTML =
            '<div class="notice-msg"><span>No data available. Please enable at least one chart option (Purchases or Revenue).</span></div>';
        return;
    }

    // Check for warning messages from backend
    if (chartData.warning) {
        console.warn('Chart data warning: ' + chartData.warning);
        document.getElementById('chart-container').innerHTML =
            '<div class="notice-msg"><span>' + chartData.warning + '</span></div>';
        return;
    }

    // Format labels based on preferred format
    var formattedLabels = chartData.primary.labels.map(function(label) {
        return formatChartDate(label, chartData.grouping);
    });

    var datasets = [];

    // Add purchases data if available
    if (chartData.primary.data && chartData.primary.data.length > 0) {
        var primaryData = chartData.primary.data.map(function(value) {
            return parseInt(value) || 0;
        });

        datasets.push({
            label: '<?php echo $this->escapeHtml($this->__('Purchase Count - Current Period')) ?>',
            data: primaryData,
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1,
            yAxisID: 'y'
        });
    }

    // Add revenue data if available
    if (chartData.primary.revenue && chartData.primary.revenue.length > 0) {
        var primaryRevenue = chartData.primary.revenue.map(function(value) {
            return parseFloat(value) || 0;
        });

        datasets.push({
            label: '<?php echo $this->escapeHtml($this->__('Revenue - Current Period')) ?>',
            data: primaryRevenue,
            borderColor: 'rgb(255, 159, 64)',
            backgroundColor: 'rgba(255, 159, 64, 0.2)',
            tension: 0.1,
            yAxisID: 'y1'
        });
    }

    // Add comparison data if available
    if (chartData.comparison && chartData.comparison.labels) {
        if (chartData.comparison.data && chartData.comparison.data.length > 0) {
            var comparisonData = chartData.comparison.data.map(function(value) {
                return parseInt(value) || 0;
            });

            datasets.push({
                label: '<?php echo $this->escapeHtml($this->__('Purchase Count - Comparison Period')) ?>',
                data: comparisonData,
                borderColor: 'rgb(255, 99, 132)',
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                tension: 0.1,
                yAxisID: 'y'
            });
        }

        if (chartData.comparison.revenue && chartData.comparison.revenue.length > 0) {
            var comparisonRevenue = chartData.comparison.revenue.map(function(value) {
                return parseFloat(value) || 0;
            });

            datasets.push({
                label: '<?php echo $this->escapeHtml($this->__('Revenue - Comparison Period')) ?>',
                data: comparisonRevenue,
                borderColor: 'rgb(153, 102, 255)',
                backgroundColor: 'rgba(153, 102, 255, 0.2)',
                tension: 0.1,
                yAxisID: 'y1'
            });
        }
    }

    // Ensure we have at least one dataset to display
    if (datasets.length === 0) {
        console.warn('No datasets available for chart rendering');
        return;
    }

    purchasesChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: formattedLabels,
            datasets: datasets
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                mode: 'index',
                intersect: false,
            },
            scales: (function() {
                var scales = {};

                // Check if we have purchase count datasets
                var hasPurchaseData = datasets.some(function(dataset) {
                    return dataset.yAxisID === 'y';
                });

                // Check if we have revenue datasets
                var hasRevenueData = datasets.some(function(dataset) {
                    return dataset.yAxisID === 'y1';
                });

                // Add purchase count axis if needed
                if (hasPurchaseData) {
                    scales.y = {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '<?php echo $this->escapeHtml($this->__('Purchase Count')) ?>'
                        },
                        ticks: {
                            stepSize: 1
                        }
                    };
                }

                // Add revenue axis if needed
                if (hasRevenueData) {
                    scales.y1 = {
                        type: 'linear',
                        display: true,
                        position: hasPurchaseData ? 'right' : 'left',
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '<?php echo $this->escapeHtml($this->__('Revenue')) ?>'
                        },
                        grid: {
                            drawOnChartArea: hasPurchaseData ? false : true,
                        }
                    };
                }

                return scales;
            })(),
            plugins: {
                title: {
                    display: true,
                    text: '<?php echo $this->escapeHtml($this->__('Sales Chart')) ?>'
                },
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            var label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            if (context.dataset.yAxisID === 'y1') {
                                // Format revenue with currency
                                label += '<?php echo Mage::app()->getLocale()->currency(Mage::app()->getStore()->getCurrentCurrencyCode())->getSymbol() ?>' + context.parsed.y.toFixed(2);
                            } else {
                                // Format count as integer
                                label += context.parsed.y;
                            }
                            return label;
                        }
                    }
                }
            }
        }
    });
}

// Format chart dates based on preferred format
function formatChartDate(dateStr, grouping) {
    try {
        // Parse the date string - handle different formats
        var date;
        if (dateStr.match(/^\d{4}-\d{2}-\d{2}$/)) {
            // YYYY-MM-DD format
            var parts = dateStr.split('-');
            date = new Date(parts[0], parts[1] - 1, parts[2]);
        } else if (dateStr.match(/^\d{4}-\d{2}$/)) {
            // YYYY-MM format (for months)
            var parts = dateStr.split('-');
            date = new Date(parts[0], parts[1] - 1, 1);
        } else if (dateStr.match(/^\d{4}-W\d{1,2}$/)) {
            // YYYY-WXX format (for weeks)
            var year = parseInt(dateStr.substring(0, 4));
            var week = parseInt(dateStr.substring(6));
            date = getDateFromWeek(year, week);
        } else {
            // Try to parse as-is
            date = new Date(dateStr);
        }

        // Check if date is valid
        if (isNaN(date.getTime())) {
            return dateStr; // Return original if parsing failed
        }

        switch(grouping) {
            case 'week':
                // For weeks, show week start date as DD-MM-YY
                var day = String(date.getDate()).padStart(2, '0');
                var month = String(date.getMonth() + 1).padStart(2, '0');
                var year = String(date.getFullYear()).substring(2);
                return day + '-' + month + '-' + year + ' (Week)';
            case 'month':
                // For months, show MM-YYYY
                var month = String(date.getMonth() + 1).padStart(2, '0');
                var year = date.getFullYear();
                return month + '-' + year;
            default: // day
                // For days, show DD-MM-YY
                var day = String(date.getDate()).padStart(2, '0');
                var month = String(date.getMonth() + 1).padStart(2, '0');
                var year = String(date.getFullYear()).substring(2);
                return day + '-' + month + '-' + year;
        }
    } catch(e) {
        console.log('Date formatting error:', e, 'for date:', dateStr);
        return dateStr; // Fallback to original string
    }
}

// Helper function to get date from year and week number (ISO week)
function getDateFromWeek(year, week) {
    // Create January 4th of the year (always in week 1)
    var jan4 = new Date(year, 0, 4);

    // Find the Monday of week 1
    var dayOfWeek = jan4.getDay();
    var mondayOffset = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;
    var week1Monday = new Date(jan4.getTime() + mondayOffset * 24 * 60 * 60 * 1000);

    // Calculate the Monday of the target week
    var targetWeekMonday = new Date(week1Monday.getTime() + (week - 1) * 7 * 24 * 60 * 60 * 1000);

    return targetWeekMonday;
}

document.observe('dom:loaded', function() {
    // Initial chart render
    renderPurchasesChart();

    // Add event listener for chart grouping change
    var groupingSelect = $('chart_grouping');
    if (groupingSelect) {
        groupingSelect.observe('change', function() {
            // Update the hidden field with the new grouping value
            var hiddenGrouping = $('hidden_chart_grouping');
            if (hiddenGrouping) {
                hiddenGrouping.value = groupingSelect.value;
            }

            // Preserve toggle states when changing grouping
            var purchasesToggle = document.getElementById('purchases_toggle');
            var revenueToggle = document.getElementById('revenue_toggle');
            var form = document.getElementById('filter_form');

            if (purchasesToggle && revenueToggle && form) {
                // Add hidden fields for both toggles to preserve their state
                var hiddenPurchases = document.getElementById('hidden_include_purchases');
                if (!hiddenPurchases) {
                    hiddenPurchases = document.createElement('input');
                    hiddenPurchases.type = 'hidden';
                    hiddenPurchases.name = 'include_purchases';
                    hiddenPurchases.id = 'hidden_include_purchases';
                    form.appendChild(hiddenPurchases);
                }

                var hiddenRevenue = document.getElementById('hidden_include_revenue');
                if (!hiddenRevenue) {
                    hiddenRevenue = document.createElement('input');
                    hiddenRevenue.type = 'hidden';
                    hiddenRevenue.name = 'include_revenue';
                    hiddenRevenue.id = 'hidden_include_revenue';
                    form.appendChild(hiddenRevenue);
                }

                // Set both values to preserve state
                hiddenPurchases.value = purchasesToggle.checked ? '1' : '0';
                hiddenRevenue.value = revenueToggle.checked ? '1' : '0';
            }

            // Submit the form to reload with new grouping
            var form = $('filter_form');
            if (form) {
                form.submit();
            }
        });
    }
});
<?php endif; ?>
</script>

<style type="text/css">
/* Custom styling for Sales Statistics section only */
.sales-stats-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    padding: 20px;
    margin: 10px 0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.sales-stats-section .grid {
    background: white;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.sales-stats-section table.data {
    width: 100%;
    border-collapse: collapse;
}

.sales-stats-section table.data thead tr.headings {
    background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
    color: white;
}

.sales-stats-section table.data thead th {
    padding: 15px 12px;
    font-weight: 600;
    text-align: left;
    border: none;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.sales-stats-section table.data tbody tr {
    border-bottom: 1px solid #e9ecef;
    transition: background-color 0.2s ease;
}

.sales-stats-section table.data tbody tr:hover {
    background-color: #f8f9fa;
}

.sales-stats-section table.data tbody tr:last-child {
    border-bottom: none;
}

.sales-stats-section table.data tbody td {
    padding: 12px;
    vertical-align: middle;
    border: none;
    font-size: 13px;
}

.sales-stats-section table.data tbody td:first-child {
    font-weight: 600;
    color: #495057;
}

.sales-stats-section table.data tbody td:nth-child(2),
.sales-stats-section table.data tbody td:nth-child(3) {
    font-weight: 600;
    color: #2c3e50;
    font-size: 14px;
}

.sales-stats-section .notice {
    color: #28a745;
    font-weight: 600;
    background: rgba(40, 167, 69, 0.1);
    padding: 4px 8px;
    border-radius: 4px;
    display: inline-block;
}

.sales-stats-section .error {
    color: #dc3545;
    font-weight: 600;
    background: rgba(220, 53, 69, 0.1);
    padding: 4px 8px;
    border-radius: 4px;
    display: inline-block;
}

.sales-stats-section em {
    background: #ffc107;
    color: #212529;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-style: normal;
    font-weight: 600;
    margin-left: 5px;
}

/* Status breakdown styling */
.status-display {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.status-toggle {
    background: none;
    border: none;
    cursor: pointer;
    padding: 2px 6px;
    margin-left: 8px;
    border-radius: 3px;
    transition: background-color 0.2s ease;
}

.status-toggle:hover {
    background-color: #f0f0f0;
}

.toggle-icon {
    font-size: 12px;
    color: #666;
}

.status-breakdown {
    margin-top: 8px;
    padding: 8px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid #4a90e2;
}

.status-pills {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.status-pill {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border: 1px solid #90caf9;
    border-radius: 16px;
    padding: 4px 10px;
    font-size: 12px;
    color: #1565c0;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.pill-label {
    font-weight: 500;
    margin-right: 6px;
}

.pill-count {
    background: #1976d2;
    color: white;
    border-radius: 10px;
    padding: 2px 6px;
    font-weight: 600;
    font-size: 11px;
    min-width: 18px;
    text-align: center;
}
</style>
