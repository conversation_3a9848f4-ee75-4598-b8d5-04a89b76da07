{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "6df48da94a7ba5763f9119ed1bf6c7f5", "packages": [{"name": "aoepeople/aoe_jscsststamp", "version": "v0.8.2", "source": {"type": "git", "url": "https://github.com/AOEpeople/Aoe_JsCssTstamp.git", "reference": "2009e68229f0f5007ff792b795e3d11d9668441c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/AOEpeople/Aoe_JsCssTstamp/zipball/2009e68229f0f5007ff792b795e3d11d9668441c", "reference": "2009e68229f0f5007ff792b795e3d11d9668441c", "shasum": ""}, "require": {"magento-hackathon/magento-composer-installer": "*"}, "type": "magento-module", "license": ["GPLv3"], "authors": [{"name": "Fabrizio <PERSON>ran<PERSON>", "email": "<EMAIL>"}], "description": "Automatic Versioning of JS and CSS files for Magento.", "homepage": "https://github.com/AOEpeople/Aoe_JsCssTstamp", "support": {"source": "https://github.com/AOEpeople/Aoe_JsCssTstamp/tree/v0.8.2", "issues": "https://github.com/AOEpeople/Aoe_JsCssTstamp/issues"}, "time": "2018-01-31T14:52:14+00:00"}, {"name": "aoepeople/aoe_scheduler", "version": "v1.5.1", "source": {"type": "git", "url": "https://github.com/AOEpeople/Aoe_Scheduler.git", "reference": "9510692d6155feb3345e32151e5cd6492f162ce5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/AOEpeople/Aoe_Scheduler/zipball/9510692d6155feb3345e32151e5cd6492f162ce5", "reference": "9510692d6155feb3345e32151e5cd6492f162ce5", "shasum": ""}, "require": {"magento-hackathon/magento-composer-installer": "*", "php": ">=5.3"}, "replace": {"fbrnc/aoe_scheduler": "*"}, "type": "magento-module", "license": ["GPL-3.0"], "authors": [{"name": "Fabrizio <PERSON>ran<PERSON>", "email": "<EMAIL>", "role": "Author/Maintainer"}, {"name": "Lee Saferite", "email": "<EMAIL>", "role": "Contributor/Maintainer"}], "description": "Magento Cron Scheduler", "homepage": "https://github.com/AOEpeople/Aoe_Scheduler", "support": {"source": "https://github.com/AOEpeople/Aoe_Scheduler/tree/v1.5.1", "issues": "https://github.com/AOEpeople/Aoe_Scheduler/issues"}, "time": "2016-12-14T14:42:05+00:00"}, {"name": "connect20/BL_CustomGrid", "version": "*******", "dist": {"type": "tar", "url": "https://connect20.magentocommerce.com/community/BL_CustomGrid/*******/BL_CustomGrid-*******.tgz"}, "suggest": {"magento-hackathon/magento-composer-installer": "Allows to manage this package as a dependency"}, "type": "magento-module", "extra": {"package-xml": "package.xml"}, "license": ["Open-Source-License"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Allows to customize all admin grids and add to it lots of useful features and new columns.\r\nWith an in-grid editor, to update values directly from the grids."}, {"name": "connect20/Fishpigs_Attribute_Splash_Page", "version": "********", "dist": {"type": "tar", "url": "https://connect20.magentocommerce.com/community/Fishpigs_Attribute_Splash_Page/********/Fishpigs_Attribute_Splash_Page-********.tgz"}, "suggest": {"magento-hackathon/magento-composer-installer": "Allows to manage this package as a dependency"}, "type": "magento-module", "extra": {"package-xml": "package.xml"}, "license": ["FishPig-EULA"], "authors": [{"name": "fishpig", "email": "<EMAIL>"}], "description": "Create SEO landing pages for your product attributes."}, {"name": "connect20/Flagbit_ChangeAttributeSet", "version": "1.0.4", "dist": {"type": "tar", "url": "https://connect20.magentocommerce.com/community/Flagbit_ChangeAttributeSet/1.0.4/Flagbit_ChangeAttributeSet-1.0.4.tgz"}, "require": {"connect20/tbt_enhancedgrid": "*"}, "suggest": {"magento-hackathon/magento-composer-installer": "Allows to manage this package as a dependency"}, "type": "magento-module", "extra": {"package-xml": "package.xml"}, "license": ["GPL"], "authors": [{"name": "Flagbit GmbH ", "email": "<EMAIL>"}], "description": "In Magento every product has a fixed attibute set. This module enables you to switch it."}, {"name": "connect20/TBT_Enhancedgrid", "version": "1.031", "dist": {"type": "tar", "url": "https://connect20.magentocommerce.com/community/TBT_Enhancedgrid/1.031/TBT_Enhancedgrid-1.031.tgz"}, "suggest": {"magento-hackathon/magento-composer-installer": "Allows to manage this package as a dependency"}, "type": "magento-module", "extra": {"package-xml": "package.xml"}, "license": ["OSL-3.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Enhances the product grid to add a bunch of convenient features."}, {"name": "defcon2/imaclean", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/sreichel/magento-Defcon2-Imaclean.git", "reference": "8c3245bb96a12c619ba035c242b3300b656c9d6f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sreichel/magento-Defcon2-Imaclean/zipball/8c3245bb96a12c619ba035c242b3300b656c9d6f", "reference": "8c3245bb96a12c619ba035c242b3300b656c9d6f", "shasum": ""}, "require": {"magento-hackathon/magento-composer-installer": "*"}, "type": "magento-module", "license": ["OSL-3.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Author/Maintainer"}], "description": "This extension allows to list all the product images that already are not in use and to erase them.", "homepage": "https://www.magentocommerce.com/magento-connect/image-clean.html", "support": {"source": "https://github.com/sreichel/magento-Defcon2-Imaclean/tree/1.2.1", "issues": "https://github.com/sreichel/magento-Defcon2-Imaclean/issues"}, "time": "2016-05-10T13:57:11+00:00"}, {"name": "eloquent/composer-config-reader", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/eloquent/composer-config-reader.git", "reference": "ee83a1cad8f68508f05b30c376003045a7cdfcd8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/eloquent/composer-config-reader/zipball/ee83a1cad8f68508f05b30c376003045a7cdfcd8", "reference": "ee83a1cad8f68508f05b30c376003045a7cdfcd8", "shasum": ""}, "require": {"eloquent/enumeration": "^5", "justinrainbow/json-schema": "^4", "php": ">=5.3"}, "require-dev": {"eloquent/liberator": "^2", "eloquent/phony": "0.14.4", "phpunit/phpunit": "^4"}, "type": "library", "autoload": {"psr-4": {"Eloquent\\Composer\\Configuration\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ezzatron.com/"}], "description": "A light-weight component for reading Composer configuration files.", "homepage": "https://github.com/eloquent/composer-config-reader", "keywords": ["composer", "configuration", "parser", "reader"], "time": "2017-11-02T01:07:08+00:00"}, {"name": "eloquent/enumeration", "version": "5.1.1", "source": {"type": "git", "url": "https://github.com/eloquent/enumeration.git", "reference": "0242859435d9b135939816858348556d3cde9e3c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/eloquent/enumeration/zipball/0242859435d9b135939816858348556d3cde9e3c", "reference": "0242859435d9b135939816858348556d3cde9e3c", "shasum": ""}, "require": {"php": ">=5.3"}, "require-dev": {"icecave/archer": "dev-develop", "phpunit/phpunit": "^4", "sami/sami": "^3"}, "type": "library", "autoload": {"psr-4": {"Eloquent\\Enumeration\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ezzatron.com/"}], "description": "An enumeration implementation for PHP.", "homepage": "https://github.com/eloquent/enumeration", "keywords": ["class", "enum", "enumeration", "multiton", "set", "type"], "time": "2015-11-03T22:21:38+00:00"}, {"name": "extensa/econt", "version": "v1.4.0.3", "source": {"type": "git", "url": "***********************:magedev/extensa-econt.git", "reference": "6e4d386b5a21695080b12ee699db0d58cdeb93b2"}, "require": {"magento-hackathon/magento-composer-installer": "*"}, "type": "magento-module", "license": ["OSL-3.0"], "authors": [{"name": "Extensa Web Development Ltd.", "email": "<EMAIL>"}], "description": "Magento Extensa_Econt module", "time": "2020-07-02T12:44:59+00:00"}, {"name": "firegento/dynamiccategory", "version": "1.2.2", "source": {"type": "git", "url": "https://github.com/firegento/firegento-dynamiccategory.git", "reference": "17ef0abebf8b3f7cb00c21cd6b4a65702e271702"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/firegento/firegento-dynamiccategory/zipball/17ef0abebf8b3f7cb00c21cd6b4a65702e271702", "reference": "17ef0abebf8b3f7cb00c21cd6b4a65702e271702", "shasum": ""}, "require": {"magento-hackathon/magento-composer-installer": "*"}, "type": "magento-module", "license": ["GPL-3.0"], "description": "This extension enables you to dynamically add products to categories based on product attributes.", "homepage": "https://github.com/firegento/firegento-dynamiccategory", "support": {"source": "https://github.com/firegento/firegento-dynamiccategory/tree/1.2.2", "issues": "https://github.com/firegento/firegento-dynamiccategory/issues"}, "time": "2015-11-20T22:49:35+00:00"}, {"name": "gordon<PERSON>ti/lesti_fpc", "version": "1.4.9", "source": {"type": "git", "url": "**************:GordonLesti/Lesti_Fpc.git", "reference": "fe3b531acfeba42dab4162c6c8e5e6e08c784c87"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/GordonLesti/<PERSON><PERSON>_Fpc/zipball/fe3b531acfeba42dab4162c6c8e5e6e08c784c87", "reference": "fe3b531acfeba42dab4162c6c8e5e6e08c784c87", "shasum": ""}, "require": {"magento-hackathon/magento-composer-installer": "^3.0"}, "require-dev": {"ecomdev/ecomdev_phpunit": "^0.3.7", "phpmd/phpmd": "^2.4", "phpunit/phpunit": "4.*", "satooshi/php-coveralls": "^1.0", "sebastian/phpcpd": "^2.0", "squizlabs/php_codesniffer": "^2.5"}, "type": "magento-module", "extra": {"map": [["app/code/community/Lesti/Fpc", "app/code/community/Lesti/Fpc"], ["app/design/frontend/base/default/layout/fpc.xml", "app/design/frontend/base/default/layout/fpc.xml"], ["app/design/frontend/base/default/template/fpc", "app/design/frontend/base/default/template/fpc"], ["app/etc/modules/Lesti_Fpc.xml", "app/etc/modules/Lesti_Fpc.xml"], ["app/etc/fpc.xml.sample", "app/etc/fpc.xml.sample"]]}, "license": ["OSL-3.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://gordonlesti.com/", "role": "developer"}], "description": "Simple Magento Fullpagecache", "homepage": "https://github.com/GordonLesti/<PERSON><PERSON>_Fpc", "keywords": ["fpc"], "time": "2017-03-29T13:01:20+00:00"}, {"name": "justin<PERSON><PERSON>/json-schema", "version": "4.1.0", "source": {"type": "git", "url": "https://github.com/justinrainbow/json-schema.git", "reference": "d39c56a46b3ebe1f3696479966cd2b9f50aaa24f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/justinrainbow/json-schema/zipball/d39c56a46b3ebe1f3696479966cd2b9f50aaa24f", "reference": "d39c56a46b3ebe1f3696479966cd2b9f50aaa24f", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"json-schema/json-schema-test-suite": "1.2.0", "phpdocumentor/phpdocumentor": "~2", "phpunit/phpunit": "^4.8.22"}, "bin": ["bin/validate-json"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "autoload": {"psr-4": {"JsonSchema\\": "src/JsonSchema/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A library to validate a json schema.", "homepage": "https://github.com/justinrainbow/json-schema", "keywords": ["json", "schema"], "time": "2016-12-22T16:43:46+00:00"}, {"name": "magento-hackathon/magento-composer-installer", "version": "3.0.x-dev", "source": {"type": "git", "url": "https://github.com/Co<PERSON>a/magento-composer-installer.git", "reference": "49257eef08a37ebc5e1e9ffaab438d00a14a578f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Coty<PERSON>/magento-composer-installer/zipball/49257eef08a37ebc5e1e9ffaab438d00a14a578f", "reference": "49257eef08a37ebc5e1e9ffaab438d00a14a578f", "shasum": ""}, "archive": {"exclude": ["vendor", "/tests/FullStackTest/"]}, "require": {"composer-plugin-api": "^1.0", "eloquent/composer-config-reader": "2.*", "php": ">=5.5", "symfony/console": "^2.5|^3.0"}, "require-dev": {"composer/composer": "1.0.*", "cotya/composer-test-framework": "dev-master", "mikey179/vfsstream": "~1.4", "phpunit/phpunit": "~4.3", "phpunit/phpunit-mock-objects": "~2.3", "squizlabs/php_codesniffer": "~2.1", "symfony/process": "~2.5"}, "suggest": {"colinmollenhour/modman": "*", "theseer/autoload": "~1.14"}, "bin": ["bin/magento-composer-installer.php"], "type": "composer-plugin", "extra": {"class": "MagentoHackathon\\Composer\\Magento\\Plugin"}, "autoload": {"psr-0": {"MagentoHackathon\\Composer": "src/"}}, "autoload-dev": {"psr-0": {"MagentoHackathon\\Composer\\Magento": "tests/"}}, "license": ["OSL-3.0"], "authors": [{"name": "<PERSON> aka <PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Composer installer for Magento modules", "homepage": "https://github.com/magento-hackathon/magento-composer-installer", "keywords": ["composer-installer", "magento"], "support": {"source": "https://github.com/Co<PERSON>a/magento-composer-installer/tree/3.1.0", "issues": "https://github.com/Co<PERSON>a/magento-composer-installer/issues"}, "time": "2017-02-23T13:23:06+00:00"}, {"name": "marko-m/inchoo_socialconnect", "version": "0.3.8", "source": {"type": "git", "url": "https://github.com/Marko-M/Inchoo_SocialConnect.git", "reference": "3f454c028d153313a3120bcc7f4eb056ca88a108"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Marko-M/Inchoo_SocialConnect/zipball/3f454c028d153313a3120bcc7f4eb056ca88a108", "reference": "3f454c028d153313a3120bcc7f4eb056ca88a108", "shasum": ""}, "require": {"magento-hackathon/magento-composer-installer": "*"}, "type": "magento-module", "notification-url": "https://packagist.org/downloads/", "license": ["OSL-3.0"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Magento extension allowing your customers to login or create an account at your store using their Google, Facebook, Twitter or LinkedIn.", "homepage": "http://www.techytalk.info/magento/socialconnect/", "abandoned": true, "time": "2016-07-13T13:44:57+00:00"}, {"name": "psr/log", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "0f73288fd15629204f9d42b7055f72dacbe811fc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/0f73288fd15629204f9d42b7055f72dacbe811fc", "reference": "0f73288fd15629204f9d42b7055f72dacbe811fc", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "time": "2020-03-23T09:12:05+00:00"}, {"name": "stenik/admin", "version": "v1.0.4", "source": {"type": "git", "url": "***********************:magedev/admin.git", "reference": "df16f9fb45c26b463fa70ebd76fafa6b846b0a2b"}, "require": {"magento-hackathon/magento-composer-installer": "*"}, "type": "magento-module", "license": ["OSL-3.0"], "authors": [{"name": "Stenik Magento Team", "email": "<EMAIL>"}], "description": "Ma<PERSON><PERSON> Admin <PERSON>", "time": "2017-10-31T12:34:03+00:00"}, {"name": "stenik/admin-productgridcategoryfilter", "version": "v1.0.0", "source": {"type": "git", "url": "***********************:magedev/admin-productgridcategoryfilter.git", "reference": "a033f74dfbc18924a8fb562afd7cbd329bd910e0"}, "require": {"magento-hackathon/magento-composer-installer": "*"}, "type": "magento-module", "license": ["OSL-3.0"], "authors": [{"name": "Stenik Magento Team", "email": "<EMAIL>"}], "description": "Magento Admin Product Grid Category Filter Module", "time": "2016-07-08T05:55:05+00:00"}, {"name": "stenik/admin-promocatalogapply", "version": "v1.0.1", "source": {"type": "git", "url": "***********************:magedev/admin-promocatalogapply.git", "reference": "fc2bdb46942455af67dcca15f543ec4c81f9e19b"}, "require": {"magento-hackathon/magento-composer-installer": "*", "stenik/base": "*"}, "type": "magento-module", "license": ["OSL-3.0"], "authors": [{"name": "Stenik Magento Team", "email": "<EMAIL>"}], "description": "Magento Admin Promo Catalog Rules Scheduler", "time": "2017-12-21T10:35:56+00:00"}, {"name": "stenik/ajaxcart", "version": "v1.0.1", "source": {"type": "git", "url": "***********************:magedev/ajaxcart.git", "reference": "5ab288fb0d0e718d28ff48ce9ad38cdeaace29ad"}, "require": {"magento-hackathon/magento-composer-installer": "*"}, "type": "magento-module", "license": ["OSL-3.0"], "authors": [{"name": "Stenik Magento Team", "email": "<EMAIL>"}], "description": "Magento AjaxCart <PERSON>", "time": "2018-04-13T16:49:34+00:00"}, {"name": "stenik/base", "version": "v1.0.19", "source": {"type": "git", "url": "***********************:magedev/base.git", "reference": "fec3c8dd4387238e2488ec1681c2955f3cad0d7e"}, "require": {"magento-hackathon/magento-composer-installer": "*"}, "type": "magento-module", "license": ["OSL-3.0"], "authors": [{"name": "Stenik Magento Team", "email": "<EMAIL>"}], "description": "Magento Base Module", "time": "2018-06-13T12:38:14+00:00"}, {"name": "stenik/checkout", "version": "v1.4.7", "source": {"type": "git", "url": "***********************:magedev/checkout.git", "reference": "ee6a098ded621d1b83da6c2f3a68d5f27d02d5f4"}, "require": {"magento-hackathon/magento-composer-installer": "*"}, "type": "magento-module", "license": ["OSL-3.0"], "authors": [{"name": "Stenik Magento Team", "email": "<EMAIL>"}], "description": "Magento Stenik Checkout module", "time": "2018-09-21T11:04:05+00:00"}, {"name": "stenik/checkout-comment", "version": "v1.0.0", "source": {"type": "git", "url": "***********************:magedev/checkout-comment.git", "reference": "c883e478d53387472fcdbe80478139d2e5a45ddc"}, "require": {"magento-hackathon/magento-composer-installer": "*"}, "type": "magento-module", "license": ["OSL-3.0"], "authors": [{"name": "Stenik Magento Team", "email": "<EMAIL>"}], "description": "Magento Stenik_CheckoutComment Module", "time": "2017-09-04T14:30:54+00:00"}, {"name": "stenik/checkout-econt", "version": "v1.1.1", "source": {"type": "git", "url": "***********************:magedev/checkout-econt.git", "reference": "d4cedb7da391092d155bd2db3771cee4515cb6cb"}, "require": {"magento-hackathon/magento-composer-installer": "*", "stenik/checkout": ">=1.3.0"}, "type": "magento-module", "license": ["OSL-3.0"], "authors": [{"name": "Stenik Magento Team", "email": "<EMAIL>"}], "description": "Magento Stenik_CheckoutEcont module", "time": "2019-01-16T09:13:56+00:00"}, {"name": "stenik/customer-invoice-fields", "version": "v1.2.1", "source": {"type": "git", "url": "***********************:magedev/customer-invoice-fields.git", "reference": "4f50bae7c22bcc25c050387ec3ce73de3f195f74"}, "require": {"magento-hackathon/magento-composer-installer": "*", "stenik/admin": "^1.0.1"}, "type": "magento-module", "license": ["OSL-3.0"], "authors": [{"name": "Stenik Magento Team", "email": "<EMAIL>"}], "description": "Magento Stenik_CustomerInvoiceFields Module", "time": "2018-08-14T11:01:48+00:00"}, {"name": "stenik/dynamic-category", "version": "v1.0.3", "source": {"type": "git", "url": "***********************:magedev/dynamic-category.git", "reference": "ff87d71b163f8e237e6b3f93ca25a691312eb7de"}, "require": {"magento-hackathon/magento-composer-installer": "*", "stenik/base": "*"}, "type": "magento-module", "license": ["OSL-3.0"], "authors": [{"name": "Stenik Magento Team", "email": "<EMAIL>"}], "description": "Magento Dynamic Categories", "time": "2017-06-15T13:53:31+00:00"}, {"name": "stenik/facebook", "version": "v1.0.10", "source": {"type": "git", "url": "***********************:magedev/facebook.git", "reference": "cb7e8185d7cf70e9aab0657906f08b2cf6cfbb68"}, "type": "magento-module", "license": ["OSL-3.0"], "authors": [{"name": "Stenik Magento Team", "email": "<EMAIL>"}], "description": "Magento Module for Facebook Open Graph Support", "time": "2018-11-15T12:01:21+00:00"}, {"name": "stenik/featured-products", "version": "v1.0.3", "source": {"type": "git", "url": "***********************:magedev/featured-products.git", "reference": "2ea85051f01f7cddeee133a063c3c4eb74a0b850"}, "require": {"magento-hackathon/magento-composer-installer": "*"}, "type": "magento-module", "license": ["OSL-3.0"], "authors": [{"name": "Stenik Magento Team", "email": "<EMAIL>"}], "description": "Magento Featured Products Module", "time": "2015-12-11T08:24:15+00:00"}, {"name": "stenik/gdpr-compliance", "version": "v1.0.18", "source": {"type": "git", "url": "***********************:magedev/gdpr-compliance.git", "reference": "4fe1110fd3f899f0f0293b3f5adb1d0ae4af9191"}, "require": {"magento-hackathon/magento-composer-installer": "*"}, "type": "magento-module", "license": ["OSL-3.0"], "authors": [{"name": "Stenik Magento Team", "email": "<EMAIL>"}], "description": "Magento GDPR Compliance Module", "time": "2019-02-11T13:02:03+00:00"}, {"name": "stenik/leasing-tbi", "version": "v1.3.3", "source": {"type": "git", "url": "***********************:magedev/leasing-tbi.git", "reference": "bd88e990e4a662e2ffe07d37dcdcffd43a20103a"}, "require": {"magento-hackathon/magento-composer-installer": "*", "stenik/lib-phpexcel": "*"}, "type": "magento-module", "license": ["OSL-3.0"], "authors": [{"name": "Stenik Magento Team", "email": "<EMAIL>"}], "description": "Magento TBI Leasing Module", "time": "2019-02-18T16:29:52+00:00"}, {"name": "stenik/leasing-unicredit", "version": "v1.3.1", "source": {"type": "git", "url": "***********************:magedev/leasing-unicredit.git", "reference": "6ea687f35d648ccfdc8372fd6bfff894ee15cdfa"}, "require": {"magento-hackathon/magento-composer-installer": "*", "stenik/lib-phpexcel": "*"}, "type": "magento-module", "license": ["OSL-3.0"], "authors": [{"name": "Stenik Magento Team", "email": "<EMAIL>"}], "description": "Magento Payment UniCredit Module", "time": "2019-02-18T16:38:48+00:00"}, {"name": "stenik/lib-phpexcel", "version": "dev-master", "source": {"type": "git", "url": "***********************:magedev/lib-phpexcel.git", "reference": "80d94cb8d6b27c7189592bd69e563b2183e3bee5"}, "require": {"magento-hackathon/magento-composer-installer": "*"}, "type": "magento-module", "license": ["OSL-3.0"], "authors": [{"name": "Stenik Magento Team", "email": "<EMAIL>"}], "description": "Lib PHPExcel for Magento", "time": "2016-10-10T13:57:03+00:00"}, {"name": "stenik/magmi", "version": "v0.7.22", "source": {"type": "git", "url": "***********************:magedev/magmi.git", "reference": "686faab23836ebbb7a7bc288b39f005bf1d408f1"}, "require": {"magento-hackathon/magento-composer-installer": "*"}, "type": "magento-module", "license": ["OSL-3.0"], "authors": [{"name": "Stenik Magento Team", "email": "<EMAIL>"}], "description": "Magmi modified by <PERSON><PERSON><PERSON>", "time": "2015-10-15T08:40:27+00:00"}, {"name": "stenik/payment-fib", "version": "v1.4.1", "source": {"type": "git", "url": "***********************:magedev/payment-fib.git", "reference": "a29ea5aef504ddefb240543553e5c9f991378459"}, "require": {"magento-hackathon/magento-composer-installer": "*"}, "type": "magento-module", "license": ["OSL-3.0"], "authors": [{"name": "Stenik Magento Team", "email": "<EMAIL>"}], "description": "Magento FIBank Payment Module", "time": "2020-03-20T11:59:58+00:00"}, {"name": "stenik/product-export", "version": "v1.0.8", "source": {"type": "git", "url": "***********************:magedev/product-export.git", "reference": "22e089edb48cea8bf7d282edbe51aeedcd6e6259"}, "require": {"magento-hackathon/magento-composer-installer": "*", "stenik/admin": "^1.0.0"}, "type": "magento-module", "license": ["OSL-3.0"], "authors": [{"name": "Stenik Magento Team", "email": "<EMAIL>"}], "description": "Stenik Product-Export", "time": "2018-08-14T10:57:19+00:00"}, {"name": "stenik/seo-base", "version": "v1.1.5", "source": {"type": "git", "url": "***********************:magedev/seo-base.git", "reference": "3e71b78653cbb9e111e3cc1988755056aaee789f"}, "require": {"magento-hackathon/magento-composer-installer": "*", "stenik/base": ">=1.0.4"}, "type": "magento-module", "license": ["OSL-3.0"], "authors": [{"name": "Stenik Magento Team", "email": "<EMAIL>"}], "description": "Magento Seo Base Module", "time": "2016-06-16T08:19:08+00:00"}, {"name": "stenik/shop", "version": "v1.0.11", "source": {"type": "git", "url": "***********************:magedev/shop.git", "reference": "deffa68183414ec416d324da266a845aa7689f39"}, "require": {"magento-hackathon/magento-composer-installer": "*", "stenik/base": "*"}, "type": "magento-module", "license": ["OSL-3.0"], "authors": [{"name": "Stenik Magento Team", "email": "<EMAIL>"}], "description": "Magento Shop Module", "time": "2017-04-04T11:00:03+00:00"}, {"name": "stenik/site-settings", "version": "v1.0.0", "source": {"type": "git", "url": "***********************:magedev/site-settings.git", "reference": "3a68dcdfb1c12e8fa7fe722e474fab47d70e51c1"}, "require": {"magento-hackathon/magento-composer-installer": "*"}, "type": "magento-module", "license": ["OSL-3.0"], "authors": [{"name": "Stenik Magento Team", "email": "<EMAIL>"}], "description": "Magento Site Settings Module", "time": "2015-08-17T14:06:06+00:00"}, {"name": "stenik/slider", "version": "v1.0.5", "source": {"type": "git", "url": "***********************:magedev/slider.git", "reference": "58201b0aa13b258ad658d20e3c62b9d3367e5b09"}, "require": {"magento-hackathon/magento-composer-installer": "*"}, "type": "magento-module", "license": ["OSL-3.0"], "authors": [{"name": "Stenik Magento Team", "email": "<EMAIL>"}], "description": "Magento Slider <PERSON>", "time": "2016-11-09T12:02:36+00:00"}, {"name": "stenik/transactional-emails", "version": "v1.1.1", "source": {"type": "git", "url": "***********************:magedev/transactional-emails.git", "reference": "04d1bc365298e860961a56eab35809a54fca851a"}, "type": "magento-module", "license": ["OSL-3.0"], "authors": [{"name": "Stenik Magento Team", "email": "<EMAIL>"}], "description": "Package with transactional emails in bulgarian locale", "time": "2018-02-07T07:15:30+00:00"}, {"name": "symfony/console", "version": "3.4.x-dev", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "cbbaa74b947a76fdc5ef0fc4dff17f22c10e1ac2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/cbbaa74b947a76fdc5ef0fc4dff17f22c10e1ac2", "reference": "cbbaa74b947a76fdc5ef0fc4dff17f22c10e1ac2", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/debug": "~2.8|~3.0|~4.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/dependency-injection": "<3.4", "symfony/process": "<3.3"}, "provide": {"psr/log-implementation": "1.0"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "~3.3|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "symfony/event-dispatcher": "~2.8|~3.0|~4.0", "symfony/lock": "~3.4|~4.0", "symfony/process": "~3.3|~4.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Console Component", "homepage": "https://symfony.com", "time": "2020-06-30T12:50:28+00:00"}, {"name": "symfony/debug", "version": "3.4.x-dev", "source": {"type": "git", "url": "https://github.com/symfony/debug.git", "reference": "f34d2bf035609cd85ba62f6365274dab016ac44e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/debug/zipball/f34d2bf035609cd85ba62f6365274dab016ac44e", "reference": "f34d2bf035609cd85ba62f6365274dab016ac44e", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "psr/log": "~1.0"}, "conflict": {"symfony/http-kernel": ">=2.3,<2.3.24|~2.4.0|>=2.5,<2.5.9|>=2.6,<2.6.2"}, "require-dev": {"symfony/http-kernel": "~2.8|~3.0|~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Debug\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Debug Component", "homepage": "https://symfony.com", "time": "2020-06-30T17:28:29+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "7b4aab9743c30be783b73de055d24a39cf4b954f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/7b4aab9743c30be783b73de055d24a39cf4b954f", "reference": "7b4aab9743c30be783b73de055d24a39cf4b954f", "shasum": ""}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.13-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "time": "2019-11-27T14:18:11+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "dev", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": [], "platform-dev": []}