<?php
/**
 * <PERSON><PERSON><PERSON> to test abandoned cart functionality manually
 * 
 * Usage: php shell/test_abandoned_cart.php
 */

require_once dirname(__FILE__) . '/abstract.php';

class Test_Abandoned_Cart extends Mage_Shell_Abstract
{
    public function run()
    {
        echo "Testing abandoned cart functionality...\n";
        
        // Manually trigger the abandoned cart history refresh
        echo "Running abandoned cart history refresh...\n";
        
        try {
            $observer = Mage::getModel('amacart/observer');
            $observer->refreshHistory();
            echo "History refresh completed successfully.\n";
        } catch (Exception $e) {
            echo "Error during history refresh: " . $e->getMessage() . "\n";
        }
        
        // Check the results
        echo "\nChecking abandoned cart history records...\n";
        
        $historyCollection = Mage::getModel('amacart/history')->getCollection();
        $historyCollection->addFieldToFilter('created_at', array('gteq' => date('Y-m-d H:i:s', strtotime('-1 hour'))));
        $historyCollection->setOrder('created_at', 'DESC');
        
        echo "Found " . $historyCollection->getSize() . " recent history records:\n";
        
        foreach ($historyCollection as $history) {
            echo sprintf(
                "ID: %d, Quote: %d, Email: %s, Customer Name: '%s', Created: %s\n",
                $history->getId(),
                $history->getQuoteId(),
                $history->getEmail(),
                $history->getCustomerName(),
                $history->getCreatedAt()
            );
        }
        
        // Check quote2email records
        echo "\nChecking quote2email records...\n";
        
        $quote2emailCollection = Mage::getModel('amacart/quote2email')->getCollection();
        $quote2emailCollection->setOrder('quote_id', 'DESC');
        $quote2emailCollection->setPageSize(5);
        
        echo "Recent quote2email records:\n";
        foreach ($quote2emailCollection as $record) {
            echo sprintf(
                "Quote ID: %d, Email: %s\n",
                $record->getQuoteId(),
                $record->getEmail()
            );
        }
    }
    
    public function usageHelp()
    {
        return <<<USAGE
Usage:  php -f test_abandoned_cart.php

  This script manually triggers the abandoned cart history refresh
  and shows the results for testing purposes.

USAGE;
    }
}

$shell = new Test_Abandoned_Cart();
$shell->run();
