<?php
/**
 * <PERSON><PERSON>t to fix missing customer names in abandoned cart history records
 * 
 * Usage: php shell/fix_abandoned_cart_names.php
 */

require_once dirname(__FILE__) . '/abstract.php';

class Fix_Abandoned_Cart_Names extends Mage_Shell_Abstract
{
    public function run()
    {
        echo "Starting to fix abandoned cart customer names...\n";
        
        $fixed = 0;
        $total = 0;
        
        // Get all history records with missing or empty customer names
        $historyCollection = Mage::getModel('amacart/history')->getCollection();
        $historyCollection->addFieldToFilter(
            array('customer_name', 'customer_name', 'customer_name'),
            array(
                array('null' => true),
                array('eq' => ''),
                array('eq' => ' ')
            )
        );
        
        echo "Found " . $historyCollection->getSize() . " records with missing customer names.\n";
        
        foreach ($historyCollection as $history) {
            $total++;
            
            // Load the quote
            $quote = Mage::getModel('sales/quote')->load($history->getQuoteId());
            if (!$quote->getId()) {
                echo "Quote {$history->getQuoteId()} not found for history {$history->getId()}\n";
                continue;
            }
            
            // Try to get customer name using the same logic as the improved Schedule model
            $customerName = $this->_getCustomerNameForHistory($quote);
            
            if ($customerName) {
                $history->setCustomerName($customerName);
                try {
                    $history->save();
                    $fixed++;
                    echo "Fixed history {$history->getId()}: '{$customerName}'\n";
                } catch (Exception $e) {
                    echo "Error saving history {$history->getId()}: " . $e->getMessage() . "\n";
                }
            } else {
                echo "No customer name found for history {$history->getId()} (quote {$history->getQuoteId()})\n";
            }
        }
        
        echo "\nCompleted! Fixed {$fixed} out of {$total} records.\n";
    }
    
    /**
     * Get customer name for abandoned cart history
     * Same logic as in Amasty_Acart_Model_Schedule::_getCustomerNameForHistory
     *
     * @param Mage_Sales_Model_Quote $quote
     * @return string
     */
    protected function _getCustomerNameForHistory($quote)
    {
        $firstName = $quote->getCustomerFirstname();
        $lastName = $quote->getCustomerLastname();
        
        // If quote has customer name, use it
        if ($firstName && $lastName) {
            return trim($firstName . ' ' . $lastName);
        }
        
        // Fall back to billing address
        $billingAddress = $quote->getBillingAddress();
        if ($billingAddress) {
            $firstName = $billingAddress->getFirstname();
            $lastName = $billingAddress->getLastname();
            
            if ($firstName && $lastName) {
                return trim($firstName . ' ' . $lastName);
            }
            
            // If only one name is available, use it
            if ($firstName) {
                return trim($firstName);
            }
            if ($lastName) {
                return trim($lastName);
            }
        }
        
        // If customer is registered, try to get name from customer entity
        if ($quote->getCustomerId()) {
            $customer = Mage::getModel('customer/customer')->load($quote->getCustomerId());
            if ($customer->getId()) {
                $firstName = $customer->getFirstname();
                $lastName = $customer->getLastname();
                
                if ($firstName && $lastName) {
                    return trim($firstName . ' ' . $lastName);
                }
                if ($firstName) {
                    return trim($firstName);
                }
                if ($lastName) {
                    return trim($lastName);
                }
            }
        }
        
        // Return empty string if no name found
        return '';
    }
    
    public function usageHelp()
    {
        return <<<USAGE
Usage:  php -f fix_abandoned_cart_names.php

  This script fixes missing customer names in abandoned cart history records
  by extracting names from quote billing addresses or customer records.

USAGE;
    }
}

$shell = new Fix_Abandoned_Cart_Names();
$shell->run();
