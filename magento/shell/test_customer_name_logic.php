<?php
/**
 * <PERSON><PERSON><PERSON> to test the customer name extraction logic
 * 
 * Usage: php shell/test_customer_name_logic.php
 */

require_once dirname(__FILE__) . '/abstract.php';

class Test_Customer_Name_Logic extends Mage_Shell_Abstract
{
    public function run()
    {
        echo "Testing customer name extraction logic...\n";
        
        // Test with a quote that has billing address
        $quote = Mage::getModel('sales/quote')->load(173733); // This one has email: <EMAIL>
        
        if (!$quote->getId()) {
            echo "Quote 173733 not found, trying another...\n";
            $quote = Mage::getModel('sales/quote')->load(173682); // This one has email: <EMAIL>
        }
        
        if (!$quote->getId()) {
            echo "No test quotes found. Creating a test scenario...\n";
            return;
        }
        
        echo "Testing with Quote ID: " . $quote->getId() . "\n";
        echo "Quote Email: " . $quote->getCustomerEmail() . "\n";
        echo "Quote Firstname: " . $quote->getCustomerFirstname() . "\n";
        echo "Quote Lastname: " . $quote->getCustomerLastname() . "\n";
        
        // Load billing address
        $billingAddress = $quote->getBillingAddress();
        if ($billingAddress && $billingAddress->getId()) {
            echo "Billing Address ID: " . $billingAddress->getId() . "\n";
            echo "Billing Firstname: " . $billingAddress->getFirstname() . "\n";
            echo "Billing Lastname: " . $billingAddress->getLastname() . "\n";
            echo "Billing Email: " . $billingAddress->getEmail() . "\n";
        } else {
            echo "No billing address found.\n";
        }
        
        // Test our customer name extraction logic
        $schedule = Mage::getModel('amacart/schedule');
        
        // Use reflection to call the protected method
        $reflection = new ReflectionClass($schedule);
        $method = $reflection->getMethod('_getCustomerNameForHistory');
        $method->setAccessible(true);
        
        $customerName = $method->invoke($schedule, $quote);
        
        echo "Extracted Customer Name: '" . $customerName . "'\n";
        
        // Test creating a history item
        echo "\nTesting history creation...\n";
        
        $testSchedule = Mage::getModel('amacart/schedule');
        $testSchedule->setId(1); // Use existing schedule
        $testSchedule->setRuleId(1);
        
        try {
            $history = $schedule->createHistoryItem($quote, $testSchedule, 0);
            echo "History created successfully!\n";
            echo "History ID: " . $history->getId() . "\n";
            echo "History Email: " . $history->getEmail() . "\n";
            echo "History Customer Name: '" . $history->getCustomerName() . "'\n";
        } catch (Exception $e) {
            echo "Error creating history: " . $e->getMessage() . "\n";
        }
    }
    
    public function usageHelp()
    {
        return <<<USAGE
Usage:  php -f test_customer_name_logic.php

  This script tests the customer name extraction logic
  with real quote data.

USAGE;
    }
}

$shell = new Test_Customer_Name_Logic();
$shell->run();
