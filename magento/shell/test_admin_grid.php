<?php
require_once dirname(__FILE__) . '/../app/Mage.php';
Mage::app('admin');

// Simulate admin session
$session = Mage::getSingleton('admin/session');
$user = Mage::getModel('admin/user')->loadByUsername('testadmin');
if ($user->getId()) {
    $session->setUser($user);
    $session->setAcl(Mage::getResourceModel('admin/acl')->loadAcl());
}

echo "Testing Admin Grid Collection...\n\n";

// Test the grid block directly
$block = Mage::app()->getLayout()->createBlock('amacart/adminhtml_rule_edit_tab_test');

// Set a rule ID (assuming rule ID 1 exists)
$block->setData('id', 1);

// Prepare the collection using reflection
$reflection = new ReflectionClass($block);
$method = $reflection->getMethod('_prepareCollection');
$method->setAccessible(true);
$method->invoke($block);
$collection = $block->getCollection();

echo "Collection SQL:\n";
echo $collection->getSelect()->__toString() . "\n\n";

echo "Collection Size: " . $collection->getSize() . "\n\n";

$count = 0;
foreach ($collection as $item) {
    $count++;
    if ($count > 3) break; // Show only first 3
    
    echo "Quote ID: " . $item->getEntityId() . "\n";
    echo "Customer Email: " . $item->getCustomerEmail() . "\n";
    echo "Target Email: " . $item->getTargetEmail() . "\n";
    echo "Customer Name: " . $item->getCustomerName() . "\n";
    echo "Customer Firstname: " . $item->getCustomerFirstname() . "\n";
    echo "Customer Lastname: " . $item->getCustomerLastname() . "\n";
    echo "---\n";
}

// Test the grid HTML output
echo "\nTesting Grid HTML Output...\n";
try {
    $columnsMethod = $reflection->getMethod('_prepareColumns');
    $columnsMethod->setAccessible(true);
    $columnsMethod->invoke($block);

    // Check if collection has data
    echo "Collection has " . $collection->count() . " items\n";

    $html = $block->toHtml();
    echo "HTML length: " . strlen($html) . " characters\n";
} catch (Exception $e) {
    echo "Error generating HTML: " . $e->getMessage() . "\n";
    $html = '';
}

// Look for email data in the HTML
if (strpos($html, '<EMAIL>') !== false) {
    echo "✓ Email found in HTML output\n";
} else {
    echo "✗ Email NOT found in HTML output\n";
}

if (strpos($html, 'target_email') !== false) {
    echo "✓ target_email field found in HTML\n";
} else {
    echo "✗ target_email field NOT found in HTML\n";
}

// Save a sample of the HTML for inspection
file_put_contents('/tmp/grid_output.html', $html);
echo "Grid HTML saved to /tmp/grid_output.html\n";
