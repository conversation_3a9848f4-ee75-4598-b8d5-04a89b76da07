<?xml version="1.0"?>
<config>
    <modules>
        <PFG_CleanUrls>
            <version>1.0.0</version>
        </PFG_CleanUrls>
    </modules>
    
    <global>
        <models>
            <pfg_cleanurls>
                <class>PFG_CleanUrls_Model</class>
            </pfg_cleanurls>
            <catalog>
                <rewrite>
                    <url>PFG_CleanUrls_Model_Catalog_Url</url>
                </rewrite>
            </catalog>
        </models>
        
        <helpers>
            <pfg_cleanurls>
                <class>PFG_CleanUrls_Helper</class>
            </pfg_cleanurls>
        </helpers>

        <blocks>
            <pfg_cleanurls>
                <class>PFG_CleanUrls_Block</class>
            </pfg_cleanurls>
        </blocks>
    </global>
    
    <admin>
        <routers>
            <adminhtml>
                <args>
                    <modules>
                        <pfg_cleanurls before="Mage_Adminhtml">PFG_CleanUrls_Adminhtml</pfg_cleanurls>
                    </modules>
                </args>
            </adminhtml>
        </routers>
    </admin>


    
    <default>
        <pfg>
            <cleanurls>
                <enabled>1</enabled>
                <clean_existing>0</clean_existing>
            </cleanurls>
        </pfg>
    </default>
</config>
